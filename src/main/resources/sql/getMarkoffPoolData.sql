SELECT ds
        ,sd
        ,pool
        ,os
        ,CONCAT(ds, sd, pool, os) as poolKey
        ,home_away_ind
FROM CREW_PVIEW.POOL_PROFILE_TERMINAL
WHERE home_away_ind = 'H'
GROUP BY ds, sd, pool, os,CONCAT(ds, sd, pool, os) ,home_away_ind
UNION
SELECT ds
     ,sd
     ,pool
     ,os
     ,CONCAT(ds, sd, pool, os) as poolKey
     ,home_away_ind
FROM CREW_PVIEW.POOL_PROFILE_TERMINAL
WHERE home_away_ind <> 'H' and
        pool<PERSON>ey not in (
    select CONCAT(ds, sd, pool, os) as poolKey
    from CREW_PVIEW.POOL_PROFILE_TERMINAL
    where home_away_ind = 'H'
    )
GROUP BY ds, sd, pool, os, CONCAT(ds, sd, pool, os), home_away_ind
;