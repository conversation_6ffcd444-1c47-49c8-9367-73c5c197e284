SELECT DISTINCT h.EMP_NBR
              ,ATTEND_STATE
              ,ATTEND_SUB_STATE
              ,START_TS
              ,START_FUNC_CD
              ,c.func_desc start_func_desc
              ,START_SUB_FUNC_CD
              ,sub2.sub_func_desc Start_sub_func_desc
              ,END_TS
              ,END_FUNC_CD
              ,e.func_desc end_func_desc
              ,END_SUB_FUNC_CD
              ,sub.sub_func_desc end_sub_func_desc
              ,h.RC
              ,h.LO_TYPE
              ,CYCLE_TRANS_IND
              ,h.ASGN_TS
              ,h.DS
              ,h.<PERSON>
              ,h.YARD_ASGN
              ,h.YARD_ASGN_SEQ
              ,h.YARD_ASGN_TYPE
              ,h.TRAIN_ID
              ,h.TRN_CREW_DISTR
              ,h.TRN_ORGN_DAY
              ,h.POOL AS POOL
              ,final_os
              ,h.CC
              ,STATE_MINS
              ,eah.asgn_type eah_asgn_type
              ,eah.XB eah_xb
              ,eah.DS eah_ds
              ,eah.SD eah_sd
              ,eah.pool eah_pool
              ,eah.cc eah_cc
FROM Crew_PVIEW.TE_ATTENDANCE_CYCLE_H h
         LEFT JOIN Crew_PVIEW.FUNCTION_CODE c ON h.start_func_cd = c.func_cd
         LEFT JOIN Crew_PVIEW.FUNCTION_CODE e ON h.end_func_cd = e.func_cd
         LEFT JOIN Crew_PVIEW.SUB_FUNCTION_CODE sub ON trim(sub.sub_func_cd) = trim(END_SUB_FUNC_CD)
    AND trim(END_FUNC_CD) = trim(sub.func_cd)
         LEFT JOIN Crew_PVIEW.SUB_FUNCTION_CODE sub2 ON trim(sub2.sub_func_cd) = trim(start_SUB_FUNC_CD)
    AND trim(start_FUNC_CD) = trim(sub2.func_cd)
         LEFT JOIN (
    SELECT DISTINCT train_id
                  ,sd
                  ,ds
                  ,pool
                  ,ASGN_TS
                  ,final_os
    FROM CREW_PVIEW.ROAD_ASGN_H qualify(TD_LD_TS = max(TD_LD_TS) OVER (
				PARTITION BY train_id
				,ASGN_TS
				,sd
				,ds
				,pool
				))
) r ON r.train_id = h.train_id
    AND r.sd = h.sd
    AND r.ds = h.ds
    AND r.pool = h.pool
    AND h.ASGN_TS = r.ASGN_TS
         LEFT JOIN (
    SELECT DISTINCT EMP_NBR
                  ,snapshot_dt
                  ,asgn_type
                  ,XB
                  ,DS
                  ,SD
                  ,pool
                  ,cc
    FROM crew_pview.TE_EMPLOYEE_ASSIGNMENT_H
    WHERE own_temp_duty_attch_pend_cd IN (
                                          '1'
        ,'2'
        ) qualify(OWN_TEMP_DUTY_ATTCH_PEND_CD = min(OWN_TEMP_DUTY_ATTCH_PEND_CD) OVER (
				PARTITION BY EMP_NBR
				,SNAPSHOT_DT
				)
			OR OWN_TEMP_DUTY_ATTCH_PEND_CD IS NULL)
) eah ON h.EMP_NBR = eah.EMP_NBR
    AND h.Start_DT = eah.SNAPSHOT_DT
WHERE start_dt >= ?
  AND start_dt < ?
  AND H.DS NOT IN (
                   'cr'
    ,'ih'
    ,'np'
    ,'ns'
    ,'su'
    ,'un'
    ,'va'
    ,'dm'
    ,'ec'
    ,'zz'
    ,'ct'
    ,'aw'
    ,'tn'
    )
  AND H.SD NOT IN (
                   'XX'
    ,'ya'
    ,'yd'
    ,'ym'
    ,'rp'
    )
  AND h.ds <> h.sd
  AND (
        h.lo_type IS NULL
        OR h.lo_type = ''
        OR h.LO_TYPE IN (
                         '%'
        ,'A'
        ,'B'
        ,'C'
        ,'F'
        ,'G'
        ,'H'
        ,'I'
        ,'J'
        ,'K'
        ,'L'
        ,'M'
        ,'N'
        ,'O'
        ,'P'
        ,'S'
        ,'T'
        ,'U'
        ,'V'
        ,'Y'
        ,'Z'
        )
    )
  -- TODO: This section is for testing only.
  --and h.emp_nbr in ('1191','1023914','1023990','1001186','373240','1031208', '1030978','3445','97931','101606','121794','38011','886494' )

ORDER BY h.emp_no
       ,start_ts
