
flatTransactions=false
server.port = 8099

skipTestInitialization=false

spring.profiles.active=test

spring.main.allow-bean-definition-overriding=true


MarkoffProcessorDAOImpl.getMarkoffDataSql =                                                         \n\
SELECT DISTINCT h.EMP_NBR                                                         				    \n\
	,ATTEND_STATE                                                                                   \n\
	,ATTEND_SUB_STATE                                                                               \n\
	,START_TS                                                                                       \n\
	,START_FUNC_CD                                                                                  \n\
	,c.func_desc start_func_desc                                                                    \n\
	,START_SUB_FUNC_CD                                                                              \n\
	,sub2.sub_func_desc Start_sub_func_desc                                                         \n\
	,(END_TS - START_TS DAY(4) TO minute) AS end_start_delta_day_hour_min                           \n\
	,td_day_of_week(START_TS) start_DOW_sun_1_sat_7                                                 \n\
	,END_TS                                                                                         \n\
	,END_FUNC_CD                                                                                    \n\
	,e.func_desc end_func_desc                                                                      \n\
	,END_SUB_FUNC_CD                                                                                \n\
	,sub.sub_func_desc end_sub_func_desc                                                            \n\
	,h.RC                                                                                           \n\
	,h.LO_TYPE                                                                                      \n\
	,CYCLE_TRANS_IND                                                                                \n\
	,h.ASGN_TS                                                                                      \n\
	,h.DS                                                                                           \n\
	,h.SD                                                                                           \n\
	,h.YARD_ASGN                                                                                    \n\
	,h.YARD_ASGN_SEQ                                                                                \n\
	,h.YARD_ASGN_TYPE                                                                               \n\
	,h.TRAIN_ID                                                                                     \n\
	,h.TRN_CREW_DISTR                                                                               \n\
	,h.TRN_ORGN_DAY                                                                                 \n\
	,--h.trn_orgn_dt,                                                                               \n\
	h.POOL AS POOL                                                                                  \n\
	,final_os                                                                                       \n\
	,                                                                                               \n\
	--depart_os,arvl_os,                                                                            \n\
	h.CC                                                                                            \n\
	,STATE_MINS                                                                                     \n\
	,eah.asgn_type eah_asgn_type                                                                    \n\
	,eah.XB eah_xb                                                                                  \n\
	,eah.DS eah_ds                                                                                  \n\
	,eah.SD eah_sd                                                                                  \n\
	,eah.pool eah_pool                                                                              \n\
	,eah.cc eah_cc                                                                                  \n\
FROM Crew_PVIEW.TE_ATTENDANCE_CYCLE_H h                                                             \n\
LEFT JOIN Crew_PVIEW.FUNCTION_CODE c ON h.start_func_cd = c.func_cd                                 \n\
LEFT JOIN Crew_PVIEW.FUNCTION_CODE e ON h.end_func_cd = e.func_cd                                   \n\
LEFT JOIN Crew_PVIEW.SUB_FUNCTION_CODE sub ON trim(sub.sub_func_cd) = trim(END_SUB_FUNC_CD)         \n\
	AND trim(END_FUNC_CD) = trim(sub.func_cd)                                                       \n\
LEFT JOIN Crew_PVIEW.SUB_FUNCTION_CODE sub2 ON trim(sub2.sub_func_cd) = trim(start_SUB_FUNC_CD)     \n\
	AND trim(start_FUNC_CD) = trim(sub2.func_cd)                                                    \n\
LEFT JOIN (                                                                                         \n\
	SELECT DISTINCT train_id                                                                        \n\
		,sd                                                                                         \n\
		,ds                                                                                         \n\
		,pool                                                                                       \n\
		,ASGN_TS                                                                                    \n\
		,final_os                                                                                   \n\
	FROM CREW_PVIEW.ROAD_ASGN_H qualify(TD_LD_TS = max(TD_LD_TS) OVER (                             \n\
				PARTITION BY train_id                                                               \n\
				,ASGN_TS                                                                            \n\
				,sd                                                                                 \n\
				,ds                                                                                 \n\
				,pool                                                                               \n\
				))                                                                                  \n\
	) r ON r.train_id = h.train_id                                                                  \n\
	AND r.sd = h.sd                                                                                 \n\
	AND r.ds = h.ds                                                                                 \n\
	AND r.pool = h.pool                                                                             \n\
	AND h.ASGN_TS = r.ASGN_TS                                                                       \n\
LEFT JOIN (                                                                                         \n\
	SELECT DISTINCT EMP_NBR                                                                         \n\
		,snapshot_dt                                                                                \n\
		,asgn_type                                                                                  \n\
		,XB                                                                                         \n\
		,DS                                                                                         \n\
		,SD                                                                                         \n\
		,pool                                                                                       \n\
		,cc                                                                                         \n\
	FROM crew_pview.TE_EMPLOYEE_ASSIGNMENT_H                                                        \n\
	WHERE own_temp_duty_attch_pend_cd IN (                                                          \n\
			'1'                                                                                     \n\
			,'2'                                                                                    \n\
			) qualify(OWN_TEMP_DUTY_ATTCH_PEND_CD = min(OWN_TEMP_DUTY_ATTCH_PEND_CD) OVER (         \n\
				PARTITION BY EMP_NBR                                                                \n\
				,SNAPSHOT_DT                                                                        \n\
				)                                                                                   \n\
			OR OWN_TEMP_DUTY_ATTCH_PEND_CD IS NULL)                                                 \n\
    ) eah ON h.EMP_NBR = eah.EMP_NBR                                                                \n\
	AND h.Start_DT = eah.SNAPSHOT_DT                                                                \n\
WHERE start_dt >= ':startDate'                                                                      \n\
	AND start_dt < ':endDate'                                                                       \n\
	AND H.DS NOT IN (                                                                               \n\
		'cr'                                                                                        \n\
		,'ih'                                                                                       \n\
		,'np'                                                                                       \n\
		,'ns'                                                                                       \n\
		,'su'                                                                                       \n\
		,'un'                                                                                       \n\
		,'va'                                                                                       \n\
		,'dm'                                                                                       \n\
		,'ec'                                                                                       \n\
		,'zz'                                                                                       \n\
		,'ct'                                                                                       \n\
		,'aw'                                                                                       \n\
		,'tn'                                                                                       \n\
		)                                                                                           \n\
	AND H.SD NOT IN (                                                                               \n\
		'XX'                                                                                        \n\
		,'ya'                                                                                       \n\
		,'yd'                                                                                       \n\
		,'ym'                                                                                       \n\
		,'rp'                                                                                       \n\
		)                                                                                           \n\
	AND h.ds <> h.sd                                                                                \n\
	AND (                                                                                           \n\
		h.lo_type IS NULL                                                                           \n\
		OR h.lo_type = ''                                                                           \n\
		OR h.LO_TYPE IN (                                                                           \n\
			'%'                                                                                     \n\
			,'A'                                                                                    \n\
			,'B'                                                                                    \n\
			,'C'                                                                                    \n\
			,'F'                                                                                    \n\
			,'G'                                                                                    \n\
			,'H'                                                                                    \n\
			,'I'                                                                                    \n\
			,'J'                                                                                    \n\
			,'K'                                                                                    \n\
			,'L'                                                                                    \n\
			,'M'                                                                                    \n\
			,'N'                                                                                    \n\
			,'O'                                                                                    \n\
			,'P'                                                                                    \n\
			,'S'                                                                                    \n\
			,'T'                                                                                    \n\
			,'U'                                                                                    \n\
			,'V'                                                                                    \n\
			,'Y'                                                                                    \n\
			,'Z'                                                                                    \n\
			)                                                                                       \n\
		)                                                                                           \n\
ORDER BY h.emp_no                                                                                   \n\
	,start_ts;                                                                                      \n\
