package com.nscorp.ccp.common.markoff;

import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.nscorp.ccp.utils.json.JsonUtils;
import lombok.*;
import lombok.extern.jackson.Jacksonized;
import org.springframework.data.annotation.*;
import org.springframework.data.relational.core.mapping.Table;

@Value
@Builder(toBuilder = true)
@Jacksonized
@Table(schema = "CCP", name = "MARKOFF_DATA")
@JsonPropertyOrder(alphabetic = true)
@With
public class MarkoffData {

	@With @Id Long markoffDataId ;

	@PersistenceCreator
	public MarkoffData(Long markoffDataId, Long markoffStudyId, String distr, String subDistr, String poolName, String craft, Long distinctCntEmp, Double empTotalStateDaysMedian, Double totalStateDays, Double stateDays, Double rate,
			Double empTotalStateDaysAvg) {
		this.markoffDataId = markoffDataId;
		this.markoffStudyId = markoffStudyId;
		this.avlUnv = null;
		this.distr = distr;
		this.subDistr = subDistr;
		this.poolName = poolName;
		this.craft = craft;
		this.distinctCntEmp = distinctCntEmp;
		this.empTotalStateDaysMedian = empTotalStateDaysMedian;
		this.totalStateDays = totalStateDays;
		this.stateDays = stateDays;
		this.rate = rate;
		this.empTotalStateDaysAvg = empTotalStateDaysAvg;
	}

	public MarkoffData(Long markoffDataId, Long markoffStudyId, String avlUnv, String distr, String subDistr, String poolName, String craft, Long distinctCntEmp, Double empTotalStateDaysMedian, Double totalStateDays, Double stateDays, Double rate,
			Double empTotalStateDaysAvg) {
		this.markoffDataId = markoffDataId;
		this.markoffStudyId = markoffStudyId;
		this.avlUnv = avlUnv;
		this.distr = distr;
		this.subDistr = subDistr;
		this.poolName = poolName;
		this.craft = craft;
		this.distinctCntEmp = distinctCntEmp;
		this.empTotalStateDaysMedian = empTotalStateDaysMedian;
		this.totalStateDays = totalStateDays;
		this.stateDays = stateDays;
		this.rate = rate;
		this.empTotalStateDaysAvg = empTotalStateDaysAvg;
	}

	Long markoffStudyId;
	@Transient
	String avlUnv; //does not get posted to DB
	String distr; //ds
	String subDistr; //sd
	String poolName;
	String craft; //cc
	Long distinctCntEmp; //re-typed from Double to Long
	Double empTotalStateDaysMedian;
	Double totalStateDays;
	Double stateDays;
	Double rate;
	Double empTotalStateDaysAvg;


	@Override
	public String toString() {
		return JsonUtils.toJsonDebuggingString(this);
	}

}
