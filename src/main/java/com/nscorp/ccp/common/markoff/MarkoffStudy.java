package com.nscorp.ccp.common.markoff;

import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.nscorp.ccp.utils.json.JsonUtils;
import lombok.*;
import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.Table;
import java.time.Instant;
import java.time.LocalDate;

@Value
@Builder(toBuilder = true)
@AllArgsConstructor
@Table("MARKOFF_STUDY")
@JsonPropertyOrder(alphabetic = true)
@With
public class MarkoffStudy {
    @Id Long markoffStudyId;
    Instant runTs;
    LocalDate startDate;
    LocalDate endDate;
    MarkoffStudyStatus status;
    String description;
    String creationUser;
    String errorMessage;

    @Override
    public String toString() {
        return JsonUtils.toJsonDebuggingString(this);
    }
}
