package com.nscorp.ccp.dao.impl.markoff;


import com.nscorp.ccp.common.markoff.MarkoffData;
import com.nscorp.ccp.common.markoff.MarkoffStudy;
import com.nscorp.ccp.dao.markoff.MarkoffAndStudyDataDAO;
import lombok.RequiredArgsConstructor;
import org.springframework.data.jdbc.repository.query.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;


@Repository
@RequiredArgsConstructor
public class MarkoffAndStudyDAOImpl implements MarkoffAndStudyDataDAO {

	private final MarkoffDataRepository markoffDataRepo;
	private final MarkoffStudyRepository studyRepo;

	@Transactional
	@Repository
	interface MarkoffDataRepository extends CrudRepository<MarkoffData, Long> {
		// @Query("select markoff_data_id, markoff_study_id, distr, sub_distr, pool_name, craft, distinct_cnt_emp, emp_total_state_days_median, total_state_days, state_days, rate, emp_total_state_days_avg from ccp.markoff_data where markoff_study_id=:markoffStudyId")
		Iterable<MarkoffData> findAllByMarkoffStudyId (Long markoffStudyId);
	}
	@Transactional
	@Repository
	interface MarkoffStudyRepository extends CrudRepository<MarkoffStudy, Long>{
		@Query("select * from ccp.markoff_study where markoff_study_id = (select max(markoff_study_id) from ccp.markoff_study where status='COMPLETED')")
		MarkoffStudy findLatestSuccessfulStudy();
	}

	@Override public MarkoffStudy getLatestSuccessfulStudy() {
		return studyRepo.findLatestSuccessfulStudy();
	}

	@Override public MarkoffStudy getStudyById(long studyId) {
		return studyRepo.findById(studyId).orElse(null);
	}

	@Override public Iterable<MarkoffStudy> getAllStudies() {
		return studyRepo.findAll();
	}

	@Override public Iterable<MarkoffData> getMarkoffDataByStudyId(long studyId) {
		return markoffDataRepo.findAllByMarkoffStudyId(studyId);
	}

	@Override public Iterable<MarkoffData> saveAll(Iterable<MarkoffData> markoffData) {
		return markoffDataRepo.saveAll(markoffData);
	}

	@Override public MarkoffStudy save(MarkoffStudy markoffStudy){
		return studyRepo.save(markoffStudy);
	}

}
