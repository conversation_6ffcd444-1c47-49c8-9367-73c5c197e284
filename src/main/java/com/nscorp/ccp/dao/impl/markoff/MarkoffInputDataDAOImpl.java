package com.nscorp.ccp.dao.impl.markoff;
import com.nscorp.ccp.common.markoff.*;
import com.nscorp.ccp.dao.markoff.MarkoffInputDataDAO;
import lombok.NonNull;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import com.google.common.base.Stopwatch;
import org.apache.commons.io.IOUtils;
import org.springframework.jdbc.core.PreparedStatementCreator;
import org.springframework.stereotype.Repository;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import javax.sql.DataSource;
import java.sql.PreparedStatement;
import java.sql.Timestamp;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.concurrent.TimeUnit;
import org.springframework.core.io.Resource;
import com.nscorp.ieorcommons.persistence.jdbc.NSDbUtils;

import static com.nscorp.ccp.utils.logging.LogUtils.info;


@Slf4j
@Repository
public class MarkoffInputDataDAOImpl implements MarkoffInputDataDAO {

    // @Value("${MarkoffProcessorDAOImpl.getMarkoffDataSql}")
    @Value("classpath:sql/getMarkoffInputData.sql")
    private Resource markoffDataSql;

    @Value("classpath:sql/getMarkoffPoolData.sql")
    private Resource markoffPoolDataSql;

    private final JdbcTemplate jdbcTemplate;

    public MarkoffInputDataDAOImpl(final @Qualifier("dataWarehouseDataSource")DataSource dataSource){
        this.jdbcTemplate = new JdbcTemplate(dataSource);
    }
    @SneakyThrows private String getMarkoffDataSql() {
        return IOUtils.toString(markoffDataSql.getInputStream(), "UTF-8");
    }
    @SneakyThrows private String getMarkoffPoolDataSql(){
        return IOUtils.toString(markoffPoolDataSql.getInputStream(), "UTF-8");
    }


    @SneakyThrows @Override
    public Iterable<MarkoffInputData> getMarkoffInputData(@NonNull final LocalDate startDate, @NonNull final Integer studyLengthMonths){
        //use the last day of the previous month from the start date as the true start Date
        final LocalDate trueEndDate = startDate.withDayOfMonth(1).minusDays(1);
        final ZoneId eastern = ZoneId.of("America/New_York");
        final Instant endInstant = trueEndDate.atStartOfDay(eastern).toInstant();
        //trueStartDate = 6 months prior to the trueEndDate (with 1 extra month to fill in later rows, but these will be cut later, so 7 months)
        final LocalDate trueStartDate = trueEndDate.minusMonths(studyLengthMonths + 1);
        System.out.println("startDatePast: " + trueStartDate);
        System.out.println("endDateFuture: " + trueEndDate);
        final PreparedStatementCreator pSC = conn -> {

            final PreparedStatement pS = conn.prepareStatement(getMarkoffDataSql());


            pS.setTimestamp(1, new Timestamp( java.sql.Date.from(trueStartDate.atStartOfDay(eastern).toInstant()).getTime() ) );
            pS.setTimestamp(2, new Timestamp(java.sql.Date.from(endInstant).getTime()));

            return pS;
        };
        val stopwatch = Stopwatch.createStarted();
        val result = jdbcTemplate.query(pSC, MARKOFF_D_ROW_MAPPER);
        if(log.isInfoEnabled()){
            info(log, String.format("Markoff Processor Query took %d milliseconds.", stopwatch.elapsed(TimeUnit.MILLISECONDS)));
        }
        return result;
    }

    @SneakyThrows @Override
    public Iterable<MarkoffInputData> getMarkoffInputDataByDates(@NonNull final LocalDate startDatePast, @NonNull final LocalDate endDateFuture, @NonNull final Integer studyLengthMonths){
        //use the last day of the previous month from the start date as the true start Date
        final ZoneId eastern = ZoneId.of("America/New_York");
        System.out.println("startDatePast: " + startDatePast);
            System.out.println("endDateFuture: " + endDateFuture);
        System.out.println("startDatePastEastern: " + startDatePast.atStartOfDay(eastern));
        System.out.println("endDateFutureEastern: " + endDateFuture.atStartOfDay(eastern));

        final PreparedStatementCreator pSC = conn -> {

            final PreparedStatement pS = conn.prepareStatement(getMarkoffDataSql());
            pS.setTimestamp(1, new Timestamp( java.sql.Date.from(startDatePast.atStartOfDay(eastern).toInstant()).getTime() ) );
            pS.setTimestamp(2, new Timestamp(java.sql.Date.from(endDateFuture.atStartOfDay(eastern).toInstant()).getTime()));

            return pS;
        };
        val stopwatch = Stopwatch.createStarted();
        val result = jdbcTemplate.query(pSC, MARKOFF_D_ROW_MAPPER);
        if(log.isInfoEnabled()){
            info(log, String.format("Markoff Processor Query took %d milliseconds.", stopwatch.elapsed(TimeUnit.MILLISECONDS)));
        }
        return result;
    }

    @SneakyThrows @Override
    public Iterable<MarkoffPrt> getMarkoffPoolData(){
        final PreparedStatementCreator pSC = conn -> {

            final PreparedStatement pS = conn.prepareStatement(getMarkoffPoolDataSql());

            return pS;
        };

        val stopwatch = Stopwatch.createStarted();
        val result = jdbcTemplate.query(pSC, MARKOFF_PRT_ROW_MAPPER);

        if(log.isInfoEnabled()){
            info(log, String.format("Markoff Pool Processor Query took %d milliseconds.", stopwatch.elapsed(TimeUnit.MILLISECONDS)));
        }
        return result;
    }

    private static final RowMapper<MarkoffPrt> MARKOFF_PRT_ROW_MAPPER = (rs, rowNum) -> MarkoffPrt.builder().
            ds(NSDbUtils.getStringColumn(rs, "ds", null)).
            sd(NSDbUtils.getStringColumn(rs, "sd", null)).
            pool(NSDbUtils.getStringColumn(rs, "pool", null)).
            os(NSDbUtils.getStringColumn(rs, "os", null)).
            poolKey(NSDbUtils.getStringColumn(rs, "poolKey", null)).
            homeAwayInd(NSDbUtils.getStringColumn(rs, "home_away_ind", null)).
            build();


    private static final RowMapper<MarkoffInputData> MARKOFF_D_ROW_MAPPER = (rs, rowNum) -> MarkoffInputData.builder().
                empNbr(NSDbUtils.getIntegerColumn(rs, "EMP_NBR", null)).
                attendState(NSDbUtils.getStringColumn(rs, "ATTEND_STATE", null)).
                attendSubState(NSDbUtils.getStringColumn(rs, "ATTEND_SUB_STATE", null)).
                startTs(NSDbUtils.getTimestampColumnAsInstant(rs, "START_TS", null)).
                startFuncCd(NSDbUtils.getStringColumn(rs, "START_FUNC_CD", null)).
                startFuncDesc(NSDbUtils.getStringColumn(rs, "start_func_desc", null)).
                startSubFuncCd(NSDbUtils.getStringColumn(rs, "START_SUB_FUNC_CD", null)).
                startSubFuncDesc(NSDbUtils.getStringColumn(rs, "Start_sub_func_desc", null)).
               // endStartDeltaDayHourMin(NSDbUtils.getTimestampColumnAsInstant(rs, "end_start_delta_day_hour_min", null)).
                //startDOWSunToSat(NSDbUtils.getIntegerColumn(rs, "start_DOW_sun_1_sat_7", null)).
                endTs(NSDbUtils.getTimestampColumnAsInstant(rs, "END_TS", null)).
                endFuncCd(NSDbUtils.getStringColumn(rs, "END_FUNC_CD", null)).
                endFuncDesc(NSDbUtils.getStringColumn(rs, "end_func_desc", null)).
                endSubFuncCd(NSDbUtils.getStringColumn(rs, "END_SUB_FUNC_CD", null)).
                endSubFuncDesc(NSDbUtils.getStringColumn(rs, "end_sub_func_desc", null)).
                rc(NSDbUtils.getStringColumn(rs, "RC", null)).
                loType(NSDbUtils.getStringColumn(rs, "LO_TYPE", null)).
                //cycleTransInd(NSDbUtils.getStringColumn(rs, "CYCLE_TRANS_IND", null)).
                //asgnTs(NSDbUtils.getTimestampColumnAsInstant(rs, "ASGN_TS", null)).
                ds(NSDbUtils.getStringColumn(rs, "DS", null)).
                sd(NSDbUtils.getStringColumn(rs, "SD", null)).
                yardAsgn(NSDbUtils.getStringColumn(rs, "YARD_ASGN", null)).
                yardAsgnSeq(NSDbUtils.getStringColumn(rs, "YARD_ASGN_SEQ", null)).
                yardAsgnType(NSDbUtils.getStringColumn(rs, "YARD_ASGN_TYPE", null)).
                trainId(NSDbUtils.getStringColumn(rs, "TRAIN_ID", null)).
                trnCrewDistr(NSDbUtils.getStringColumn(rs, "TRN_CREW_DISTR", null)).
                //trnOrgnDay(NSDbUtils.getStringColumn(rs, "TRN_ORGN_DAY", null)).
                pool(NSDbUtils.getStringColumn(rs, "POOL", null)).
                finalOs(NSDbUtils.getStringColumn(rs, "final_os", null)).
                cc(NSDbUtils.getStringColumn(rs, "CC", null)).
                stateMins(NSDbUtils.getIntegerColumn(rs, "STATE_MINS", null)).
                eahAsgnType(NSDbUtils.getStringColumn(rs, "eah_asgn_type", null)).
                eahXb(NSDbUtils.getStringColumn(rs, "eah_xb", null)).
                eahDs(NSDbUtils.getStringColumn(rs, "eah_ds", null)).
                eahSd(NSDbUtils.getStringColumn(rs, "eah_sd", null)).
                eahPool(NSDbUtils.getStringColumn(rs, "eah_pool", null)).
                eahCc(NSDbUtils.getStringColumn(rs, "eah_cc", null)).
                build();
    //departOs(NSDbUtils.getStringColumn(rs, "departOs", null)).
    // arvlOs(NSDbUtils.getStringColumn(rs, "arvlOs", null)).


}
