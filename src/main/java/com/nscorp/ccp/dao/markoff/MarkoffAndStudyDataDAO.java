package com.nscorp.ccp.dao.markoff;

import com.nscorp.ccp.common.markoff.MarkoffData;
import com.nscorp.ccp.common.markoff.MarkoffStudy;
public interface MarkoffAndStudyDataDAO {

	MarkoffStudy getLatestSuccessfulStudy();
	MarkoffStudy getStudyById(long studyId);
	Iterable<MarkoffStudy> getAllStudies();
	Iterable<MarkoffData> getMarkoffDataByStudyId(long studyId);
	Iterable<MarkoffData> saveAll(Iterable<MarkoffData> markoffData);

	MarkoffStudy save(MarkoffStudy markoffStudy);
}
