package com.nscorp.ccp.dao.markoff;
import com.nscorp.ccp.common.markoff.MarkoffInputData;
import com.nscorp.ccp.common.markoff.MarkoffPrt;

import java.time.LocalDate;
public interface MarkoffInputDataDAO {

    Iterable<MarkoffInputData> getMarkoffInputData(LocalDate startDate, Integer studyLengthMonths);
    Iterable<MarkoffInputData> getMarkoffInputDataByDates(LocalDate startDatePast, LocalDate endDateFuture, Integer studyLengthMonths);
    Iterable<MarkoffPrt> getMarkoffPoolData();

}
