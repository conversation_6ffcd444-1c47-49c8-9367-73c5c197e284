package com.nscorp.ccp;


import com.nscorp.ccp.biz.markoff.MarkoffStudyCreator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import java.time.LocalDate;

@Component
@Profile("markoff_proc")
@RequiredArgsConstructor
@Slf4j
public class MarkoffProcessorRunner implements CommandLineRunner {
	private final MarkoffStudyCreator markoffStudyCreator;
	@Override public void run(final String... args) {
		//parse studyLength months and date of running, if not present then set default
		final Integer studyLengthMonths = args != null && args.length > 0 ?  parseStudyLengthMonths(args[0]) : 6;
		final LocalDate todayDate = args != null && args.length > 0 ? parseTodayDate(args[1]) : LocalDate.now();

		 markoffStudyCreator.processMarkoffStudy(studyLengthMonths, todayDate, "sys");
	}

	public static Integer parseStudyLengthMonths(String s){
		try{
			return Integer.parseInt(s);

		}catch(Exception e){
			return 6;
		}
	}

	public static LocalDate parseTodayDate(String s){
		try{
			return LocalDate.parse(s);

		}catch(Exception e){
			return LocalDate.now();
		}
	}
}
