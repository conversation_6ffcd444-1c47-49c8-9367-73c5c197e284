package com.nscorp.ccp.config;

import com.teradata.jdbc.TeraDriver;
import com.zaxxer.hikari.HikariDataSource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.*;
import org.springframework.core.env.PropertyResolver;

import javax.sql.DataSource;

import static com.nscorp.ccp.utils.db.DataSourceUtils.simpleDriverDataSource;
import static com.nscorp.ccp.utils.logging.LogUtils.info;
import static com.nscorp.ccp.utils.logging.LogUtils.warn;

/**
 * Configuration class for data sources.
 */
@Configuration
@Slf4j
@Profile("!simpleds")
@RequiredArgsConstructor
public class DbConfiguration {
	private final PropertyResolver propertyResolver;

	/**
	 * Creates the datasource for the SCPM database.
	 * @return The data source.
	 */
	@Bean
	@Primary
	@ConfigurationProperties(prefix = "spring.scpm.datasource")
	public DataSource scpmDataSource(){
		warn(log, "scpmDataSource()");
		final HikariDataSource result = new HikariDataSource();
		result.setSchema("CCP");
		result.setConnectionInitSql("set schema " + "CCP");
		if ( log.isInfoEnabled() ) {
			info(log, String.format("ccpSchema=%s", "CCP"));
		}
		return result;
	}

	/**
	 * Connection pool for accessing the data warehouse.
	 * @return The DataSource.
	 */
	@Bean
	@ConfigurationProperties(prefix = "spring.datawarehouse.datasource")
	public DataSource dataWarehouseDataSource(){
		// return DataSourceBuilder.create().build();
		return simpleDriverDataSource(propertyResolver, "datawarehouse", TeraDriver.class, null);
	}

}
