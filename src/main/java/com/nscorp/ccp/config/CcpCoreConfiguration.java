package com.nscorp.ccp.config;

import com.nscorp.ccp.dao.impl.DaoImpl;
import com.nscorp.ccp.testing.Testing;
import com.nscorp.ccp.utils.date.DateTimeProvider;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.*;
import org.springframework.data.jdbc.repository.config.EnableJdbcRepositories;
import org.springframework.web.client.RestTemplate;

import java.time.Duration;

@Configuration
@EnableCaching
@EnableJdbcRepositories(basePackageClasses = {DaoImpl.class, Testing.class}, namedQueriesLocation = "classpath:core.properties", considerNestedRepositories = true)
@Import({DbConfiguration.class})
@Slf4j
@RequiredArgsConstructor
public class CcpCoreConfiguration {
	@Bean
	public RestTemplate restTemplate(RestTemplateBuilder builder) {
		return builder.setConnectTimeout(Duration.ofMillis(3000)).setReadTimeout(Duration.ofMillis(3000)).build();
	}

	@Autowired
	private DateTimeProvider dateTimeProvider;

}
