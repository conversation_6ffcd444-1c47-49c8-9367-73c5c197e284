package com.nscorp.ccp.rest.mappers;

import com.nscorp.ccp.common.markoff.MarkoffData;
import com.nscorp.ccp.common.markoff.MarkoffStudy;
import com.nscorp.ccp.rest.json.JsonMarkoffData;
import com.nscorp.ccp.rest.json.JsonMarkoffStudy;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface JsonMapper {
    JsonMarkoffData toJson(MarkoffData dto);
    JsonMarkoffStudy toJson(MarkoffStudy dto);
}
