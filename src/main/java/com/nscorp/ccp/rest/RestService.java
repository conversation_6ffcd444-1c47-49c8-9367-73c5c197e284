package com.nscorp.ccp.rest;

import com.google.common.collect.ImmutableList;
import com.nscorp.ccp.common.markoff.MarkoffData;
import com.nscorp.ccp.dao.markoff.MarkoffAndStudyDataDAO;
import com.nscorp.ccp.rest.json.JsonCollectionResponse;
import com.nscorp.ccp.rest.json.JsonMarkoffData;
import com.nscorp.ccp.rest.json.JsonMarkoffStudy;
import com.nscorp.ccp.rest.mappers.JsonMapper;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.IterableUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

import java.util.List;

import static com.google.common.collect.Iterables.transform;
import static com.nscorp.ccp.utils.rest.RestUtil.listResponse;
import static io.vavr.API.Stream;

/**
 * Provides rest APIs for user management and login services.
 */
@Slf4j
@RestController
@AllArgsConstructor
@Builder
@RequestMapping("markoff/")
public class RestService {
	private final MarkoffAndStudyDataDAO dao;
	private final JsonMapper jsonMapper;

	@GetMapping(path = "/markoff-data/latest")
	public ResponseEntity<JsonCollectionResponse> getAllMarkoffDataForLatestStudy(HttpServletRequest request ) {
		val study = dao.getLatestSuccessfulStudy();
		if ( study == null ) {
			return ResponseEntity.notFound().build();
		}
		val studyId = study.getMarkoffStudyId();
		val data = dao.getMarkoffDataByStudyId(studyId);

		return listResponse(request, transform(data, jsonMapper::toJson));
	}

	@GetMapping(path = "/markoff-data/study-id/{studyId}")
	public ResponseEntity<JsonCollectionResponse> getAllMarkoffData(HttpServletRequest request,
			@PathVariable long studyId) {
		val study = dao.getStudyById(studyId);
		if ( study == null ) {
			return ResponseEntity.notFound().build();
		}
		val data = dao.getMarkoffDataByStudyId(studyId);

		return listResponse(request, transform(data, jsonMapper::toJson));
	}

	@GetMapping(path = "/study")
	public ResponseEntity<JsonCollectionResponse> getAllStudies(HttpServletRequest request ) {
		val studies = dao.getAllStudies();
		return listResponse(request, transform(studies, jsonMapper::toJson));
	}

	@GetMapping(path = "/study/study-id/{studyId}")
	public ResponseEntity<JsonMarkoffStudy> getStudyById(
			@PathVariable long studyId) {
		val study = dao.getStudyById(studyId);
		if ( study == null ) {
			return ResponseEntity.notFound().build();
		}
		val toJson = jsonMapper.toJson(study);
		return ResponseEntity.ok(toJson);
	}
}
