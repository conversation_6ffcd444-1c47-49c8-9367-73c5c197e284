package com.nscorp.ccp.rest.json;
import java.util.*;
import lombok.val;
import static com.nscorp.ccp.utils.logging.LogUtils.*;

import lombok.*;
import lombok.extern.jackson.Jacksonized;

import java.util.List;

@Value
@Builder(toBuilder = true)
@Jacksonized
@AllArgsConstructor(access = AccessLevel.PUBLIC)
public class JsonCollectionResponse {
    @With String href;
    List items;
    public Integer getCount(){
      return items != null? items.size():-1;
    }
}
