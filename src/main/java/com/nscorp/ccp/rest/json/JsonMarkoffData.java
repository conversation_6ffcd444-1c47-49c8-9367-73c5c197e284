package com.nscorp.ccp.rest.json;


import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import lombok.*;
import lombok.extern.jackson.Jacksonized;

@Value
@Builder(toBuilder = true)
@Jacksonized
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@JsonPropertyOrder(alphabetic=true)
@AllArgsConstructor(access = AccessLevel.PUBLIC)
public class JsonMarkoffData {
	Long markoffDataId ;
	Long markoffStudyId;
	String distr;
	String subDistr;
	String poolName;
	String craft;
	Long distinctCntEmp;
	Double empTotalStateDaysMedian;
	Double totalStateDays;
	Double stateDays;
	Double rate;
	Double empTotalStateDaysAvg;
}
