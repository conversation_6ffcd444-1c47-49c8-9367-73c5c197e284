package com.nscorp.ccp.rest.json;


import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import lombok.*;
import lombok.extern.jackson.Jacksonized;

import java.time.Instant;
import java.time.LocalDate;

@Value
@Builder(toBuilder = true)
@Jacksonized
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@JsonPropertyOrder(alphabetic=true)
@AllArgsConstructor(access = AccessLevel.PUBLIC)
public class JsonMarkoffStudy {
	Long markoffStudyId;
	Instant runTs;
	LocalDate startDate;
	LocalDate endDate;
	String status;
	String description;
	String creationUser;
	String errorMessage;
}
