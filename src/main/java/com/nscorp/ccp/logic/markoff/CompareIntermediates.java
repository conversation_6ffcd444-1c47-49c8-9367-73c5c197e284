package com.nscorp.ccp.logic.markoff;


import com.google.common.collect.FluentIterable;
import com.nscorp.ccp.utils.lang.Pure;
import com.nscorp.ieorcommons.lang.NSObjectUtils;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.io.PrintWriter;
import java.io.Writer;
import java.time.Instant;
import java.time.LocalDateTime;
import java.util.Comparator;
import java.util.stream.Stream;

import static com.nscorp.ieorcommons.collection.JoinUtils.leftOuterJoin;
import static com.nscorp.ieorcommons.lang.NSStringUtils.trimmedBlankToNull;

@Slf4j
@Pure
public class CompareIntermediates {
	@Value
	private static class Key {
		int empNbr;
		Instant startTs;
		Instant endTs;
	}
	private Key key(Intermediate intermediate) {
		return new Key(intermediate.getEmpNbr(), intermediate.getStartTs(), intermediate.getEndTs());
	}
    private String normalize(String s) {
		return trimBlankToEmptyString(s);
    }

	public static String trimBlankToEmptyString(Object o) {
		String s = StringUtils.trim(NSObjectUtils.toString(o));
		return StringUtils.isEmpty(s) ? "" : s;
	}

	private Intermediate normalizeSpaces(Intermediate i) {
        return i.toBuilder().
            attendState       ( normalize(i.getAttendState      ())).
            attendSubState    ( normalize(i.getAttendSubState   ())).
            startFuncCd       ( normalize(i.getStartFuncCd      ())).
            startFuncDesc     ( normalize(i.getStartFuncDesc    ())).
            startSubFuncCd    ( normalize(i.getStartSubFuncCd   ())).
            startSubFuncDesc  ( normalize(i.getStartSubFuncDesc ())).
            endFuncCd         ( normalize(i.getEndFuncCd        ())).
            endFuncDesc       ( normalize(i.getEndFuncDesc      ())).
            endSubFuncCd      ( normalize(i.getEndSubFuncCd     ())).
            endSubFuncDesc    ( normalize(i.getEndSubFuncDesc   ())).
            rc                ( normalize(i.getRc               ())).
            loType            ( normalize(i.getLoType           ())).
            ds                ( normalize(i.getDs               ())).
            sd                ( normalize(i.getSd               ())).
            yardAsgn          ( normalize(i.getYardAsgn         ())).
            yardAsgnSeq       ( normalize(i.getYardAsgnSeq      ())).
            yardAsgnType      ( normalize(i.getYardAsgnType     ())).
            trainId           ( normalize(i.getTrainId          ())).
            trnCrewDistr      ( normalize(i.getTrnCrewDistr     ())).
            pool              ( normalize(i.getPool             ())).
            finalOs           ( normalize(i.getFinalOs          ())).
            cc                ( normalize(i.getCc               ())).
            eahAsgnType       ( normalize(i.getEahAsgnType      ())).
            eahXb             ( normalize(i.getEahXb            ())).
            eahDs             ( normalize(i.getEahDs            ())).
            eahSd             ( normalize(i.getEahSd            ())).
            eahPool           ( normalize(i.getEahPool          ())).
            eahCc             ( normalize(i.getEahCc            ())).
            intermediatePool  ( normalize(i.getIntermediatePool ())).
            achDs             ( normalize(i.getAchDs            ())).
            achSd             ( normalize(i.getAchSd            ())).
            homeAwayInd       ( normalize(i.getHomeAwayInd      ())).
            finalPool         ( normalize(i.getFinalPool        ())).
            awayPool          ( normalize(i.getAwayPool         ())).
            finalYardAsgn     ( normalize(i.getFinalYardAsgn    ())).
            finalVar          ( normalize(i.getFinalVar         ())).
          //  newDs             ( normalize(i.getNewDs            ())).
           // newSd             ( normalize(i.getNewSd            ())).
            achCC             ( normalize(i.getAchCC            ())).
            finalCC           ( normalize(i.getFinalCC          ())).
            build();
	}

	public void compare(Iterable<Intermediate> i1, Iterable<Intermediate> i2, Writer out1, Writer out2) {
		val comparator = Comparator.comparing(Intermediate::getEmpNbr).thenComparing(Intermediate::getStartTs).thenComparing(Intermediate::getEndTs);
		val i1Sorted = FluentIterable.from(i1).toSortedList(comparator);
		val i2Sorted = FluentIterable.from(i2).toSortedList(comparator);
		@Cleanup val pw1 = new PrintWriter(out1);
		@Cleanup val pw2 = new PrintWriter(out2);
		val trimmed1 = FluentIterable.from(i1Sorted).transform(this::normalizeSpaces);
		val trimmed2 = FluentIterable.from(i2Sorted).transform(this::normalizeSpaces);
		leftOuterJoin(trimmed1, trimmed2,
				(l,r)-> {
					if ( r.size() == 1 ) {
						if ( ! l.equals(r.get(0))) {
							val s1 = l.toString();
							val s2 = r.get(0).toString();
							pw1.println(s1);
							pw2.println(s2);
						}
					}
					else if ( r.isEmpty() ) {
						val s1 = l.toString();
						pw1.println(s1);
					}
					return Stream.of();
				},
				this::key,
				this::key);
	}
}
