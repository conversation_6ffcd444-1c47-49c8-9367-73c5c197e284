package com.nscorp.ccp.logic.markoff.processingSteps;


import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.*;
import com.google.common.collect.Streams;
import com.nscorp.ccp.common.markoff.MarkoffData;
import com.nscorp.ccp.logic.markoff.*;
import com.nscorp.ieorcommons.collection.NSIterableUtils;
import com.nscorp.ieorcommons.lang.exception.NSExceptionUtils;
import com.opencsv.bean.HeaderColumnNameTranslateMappingStrategy;
import com.opencsv.bean.StatefulBeanToCsv;
import com.opencsv.bean.StatefulBeanToCsvBuilder;
import io.vavr.Tuple;
import io.vavr.Tuple2;
import lombok.*;
import lombok.experimental.UtilityClass;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.FilenameUtils;
import org.mapstruct.factory.Mappers;

import java.io.*;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.*;

import static com.google.common.collect.Streams.stream;
import static com.nscorp.ccp.utils.json.JsonUtils.OBJECT_MAPPER;
import org.apache.commons.lang3.*;

@UtilityClass
public
class MarkoffProcessorUtils {
	static final DateTimeFormatter DTM_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");
	private static final boolean USE_JSON_FOR_INTERMEDIATE_LOGGING = false;
	private static final boolean USE_CSV_FOR_INTERMEDIATE_LOGGING = false;

	private static final boolean WRITE_FINAL_MARKOFF_DATA_LOG = false;

	public static final IntermediateMapper MAPPER = Mappers.getMapper(IntermediateMapper.class);
	public static <K> FluentIterable<Tuple2<K, FluentIterable<Intermediate>>> indexByKey(Iterable<Intermediate> intermediates, Function<Intermediate, K> keyExtractor) {
		val ents = FluentIterable.from(intermediates)
				.index(keyExtractor::apply)
				.asMap()
				.entrySet();
		return FluentIterable.from(ents).transform(ent-> Tuple.of(ent.getKey(), FluentIterable.from(ent.getValue() )));
	}

	public static FluentIterable<Tuple2<Intermediate,Intermediate>> pairs(Iterable<Intermediate> intermediates) {
		return FluentIterable.from(NSIterableUtils.pairs(intermediates));
		// return FluentIterable.from(StreamEx.of(stream(intermediates))
		// .pairMap(Tuple::of).collect(toImmutableList()));
	}

	FluentIterable<FluentIterable<Intermediate>> groupByEmpNbr(Iterable<Intermediate> intermediates) {
		return indexByKey(intermediates, Intermediate::getEmpNbr).transform(Tuple2::_2);
	}

	FluentIterable<Tuple2<Integer,FluentIterable<Intermediate>>> groupByEmpNbrWithKeys(Iterable<Intermediate> intermediates) {
		return indexByKey(intermediates, Intermediate::getEmpNbr);
	}

	static FluentIterable<Intermediate> writeIntermediates(String filename, Iterable<Intermediate> intermediates) {
		return writeIntermediates(filename, intermediates, false);
	}
	public static boolean isAttendStateW(Intermediate i) {
		return i.getAttendState().equals("W");
	}

	public static boolean isStartFunc35(Intermediate i) {
		return i.getStartFuncCd().equals("35");
	}
	public static Integer sumWorkTime(FluentIterable<Intermediate> intermediates){
		return stream(intermediates).mapToInt(i -> i.getStateMins()).sum();
	}

	public static void writeFinalMarkoffData(FluentIterable<MarkoffData> markoffData){
		if(WRITE_FINAL_MARKOFF_DATA_LOG) {
			val markoffDataFinalSorted = markoffData.toSortedList(
					Comparator.comparing(MarkoffData::getDistr).
							thenComparing(MarkoffData::getSubDistr).
							thenComparing(MarkoffData::getPoolName).
							thenComparing(MarkoffData::getCraft));

			com.nscorp.ccp.utils.io.FileUtils.writeCsv(markoffDataFinalSorted, "Final Markoff Data.csv");
			try {
				OBJECT_MAPPER.writerWithDefaultPrettyPrinter()
						.writeValue(new File("FinalMarkoffJson" + ".json"), markoffDataFinalSorted);
			} catch (IOException e) {
				throw new RuntimeException(e);
			}
		}
	}


	@SneakyThrows static FluentIterable<Intermediate> writeIntermediates(String filename, Iterable<Intermediate> intermediates, boolean noConvert) {
		if ( USE_JSON_FOR_INTERMEDIATE_LOGGING ) {

			OBJECT_MAPPER.writerWithDefaultPrettyPrinter()
					.writeValue(new File(filename + ".json"), intermediates);
		}
		if(USE_CSV_FOR_INTERMEDIATE_LOGGING){
			val csvFilename = filename + ".csv";
			if ( noConvert ) {
				writeCsv(intermediates, csvFilename);
			}
			else {
				writeCsv(makeIntermediatesToWrite(intermediates), csvFilename);
			}
		}
		return FluentIterable.from(intermediates);
	}

	public static <T> void writeCsv(Iterable<T> iterable, String filename) throws IOException {
		try {
			FileWriter writer = new FileWriter(filename);
			HeaderColumnNameTranslateMappingStrategy<IntermediateToWrite> strategy = new HeaderColumnNameTranslateMappingStrategy<>();
			strategy.setType(IntermediateToWrite.class);
			strategy.setColumnMapping(createIntermediateColumnMapping());
			try {
				StatefulBeanToCsv<T> csvWriter = (new StatefulBeanToCsvBuilder(writer))
						.withMappingStrategy(strategy)
						.build();
				Streams.stream(iterable).forEachOrdered((input) -> {
					NSExceptionUtils.sneakyRun(() -> {
						csvWriter.write(input);
					});
				});
			} finally {
				if (Collections.singletonList(writer).get(0) != null) {
					writer.close();
				}

			}

		} catch (Throwable var8) {
			throw var8;
		}
	}

	public static FluentIterable<IntermediateToWrite> makeIntermediatesToWrite(Iterable<Intermediate> intermediates){
		return FluentIterable.from(intermediates).
				transform(MAPPER::toIntermediateToWrite);
	}
	static LocalDateTime ldt(Instant instant) {
		return LocalDateTime.ofInstant(instant, ZoneId.systemDefault());
	}

	public static <T> FluentIterable<Tuple2<T,Long>> mapWithIndex(Iterable<T> iterable) {
		val s = Streams.mapWithIndex(Streams.stream(iterable), Tuple::of);
		val l = s.collect(ImmutableList.toImmutableList());
		return FluentIterable.from(l);
	}

	@SneakyThrows
	public static Iterable<Intermediate> readIntermediateFile(File file){
		final String data = FileUtils.readFileToString(file, "UTF-8");
		ObjectMapper mapper = new ObjectMapper();
		//System.out.println(data.substring(0,2000));
		final Iterable<Intermediate> readObjects = mapper.readValue(data, new TypeReference<Collection<Intermediate>>(){});
		return readObjects;


	}



	public static boolean isTimeDiffNegative(Tuple2<Intermediate, Intermediate> tup) {
		return isTimeDiffNegative(tup._2, tup._1);
	}

	static boolean isTimeDiffNegative(Intermediate current, Intermediate prev){
		/*
		6.	is.na(pre.ts.diff) | pre.ts.diff>=0
				note: pre.ts.diff is time difference this row start_ts - previous row end_ts
				(around 4-5 rows eliminated due to the data quality of table ACH)
				must sort by emp_nbr,start_ts, end_ts in order to define previous row
			-  per row, we are choosing to flag it for removal if its start ts comes before the prev row end ts
		 */

		if(current.getStartTs().isBefore(prev.getEndTs()) && current.getEmpNbr() == prev.getEmpNbr())
			return true;
		else
			return false;

	}

	public static boolean isEahAsgnTypeX(Intermediate intermediate) {
		return StringUtils.equals(intermediate.getEahAsgnType(), "X");
	}

	public static <T> FluentIterable<Intermediate> backPopulateField(T initial, FluentIterable<Intermediate> intermediates,
																	 Predicate<Intermediate> predicate,
																	 Function<Intermediate, T> extractor,
																	 BiFunction<Intermediate, T, Intermediate> mapper) {
		T currentFieldValue = initial;
		final List<Intermediate> newIntermediates = new ArrayList<>();
		for(final Intermediate current: intermediates){
			final Intermediate toAppend;
			if(predicate.test( current ) ){
				//ok to add
				currentFieldValue = extractor.apply(current);
				//toAppend = current;
				toAppend = mapper.apply(current, currentFieldValue);
			}
			else{
				toAppend = mapper.apply(current, currentFieldValue);
			}
			newIntermediates.add(toAppend);
		}
		return FluentIterable.from(newIntermediates);
	}

	static <T> FluentIterable<Intermediate> backPopulateWithPrevCheckField(T initial, FluentIterable<Intermediate> intermediates,
															  Predicate<Intermediate> predicate,
															  Function<Intermediate,T> extractor,
															  BiFunction<Intermediate,T,Intermediate> mapper) {
		T currentFieldValue = initial;
		final List<Intermediate> newIntermediates = new ArrayList<>();
		Intermediate previous = null;
		for(final Intermediate current: intermediates){
			final Intermediate toAppend;
			if(predicate.test( current ) || (previous != null &&
					org.apache.commons.lang3.StringUtils.isEmpty(previous.getFinalVar()))){
				//ok to add
				currentFieldValue = extractor.apply(current);
				//toAppend = current;
				toAppend = mapper.apply(current, currentFieldValue);
			}
			else{
				toAppend = mapper.apply(current, currentFieldValue);
			}
			previous = current;
			newIntermediates.add(toAppend);
		}
		return FluentIterable.from(newIntermediates);
	}

	public static <T> FluentIterable<T> reverseList(Iterable<T> list){
		List<T> mutableList = new ArrayList<>(ImmutableList.copyOf(list));
		Collections.reverse(mutableList);
		return FluentIterable.from(mutableList);
	}

	@SneakyThrows public static void loadAndCompare(Iterable<Intermediate> intermediates, String filename) {
		val loaded = ReadCsv.readCsv(filename);
		val baseName = FilenameUtils.removeExtension(filename);
		@Cleanup val javaFile = new FileWriter(baseName + "_java_diffs.txt");
		@Cleanup val rFile = new FileWriter(baseName + "_r_diffs.txt");
		new CompareIntermediates().compare(intermediates, loaded, javaFile, rFile);
	}
/*	@SneakyThrows public static void loadAndComparePoolData(Iterable<MarkoffPrt> intermediates, String filename) {
		val loaded = ReadCsv.readMarkoffPoolInputDataCsv(filename);
		val baseName = FilenameUtils.removeExtension(filename);
		@Cleanup val javaFile = new FileWriter(baseName + "_java_diffs.txt");
		@Cleanup val rFile = new FileWriter(baseName + "_r_diffs.txt");
		new CompareIntermediates().compare(intermediates, loaded, javaFile, rFile);
	}*/

	@SneakyThrows static Iterable<Intermediate> loadAndCompareReturnLoadData(Iterable<Intermediate> intermediates, String filename) {
		val loaded = ReadCsv.readCsv(filename);
		val baseName = FilenameUtils.removeExtension(filename);
		@Cleanup val javaFile = new FileWriter(baseName + "_java_diffs.txt");
		@Cleanup val rFile = new FileWriter(baseName + "_r_diffs.txt");
		new CompareIntermediates().compare(intermediates, loaded, javaFile, rFile);
		return loaded;
	}
	public static Map<String, String> createIntermediateColumnMapping(){
		Map<String, String> colMap = new HashMap<>();
		colMap.put("empNbr"           ,"empNbr"          );
		colMap.put("attendState"      ,"attendState"     );
		colMap.put("attendSubState"   ,"attendSubState"  );
		colMap.put("startTs"          ,"startTs"         );
		colMap.put("startFuncCd"      ,"startFuncCd"     );
		colMap.put("startFuncDesc"    ,"startFuncDesc"   );
		colMap.put("startSubFuncCd"   ,"startSubFuncCd"  );
		colMap.put("startSubFuncDesc" ,"startSubFuncDesc");
		colMap.put("endTs"            ,"endTs"           );
		colMap.put("endFuncCd"        ,"endFuncCd"       );
		colMap.put("endFuncDesc"      ,"endFuncDesc"     );
		colMap.put("endSubFuncCd"     ,"endSubFuncCd"    );
		colMap.put("endSubFuncDesc"   ,"endSubFuncDesc"  );
		colMap.put("rc"               ,"rc"              );
		colMap.put("loType"           ,"loType"          );
		colMap.put("ds"               ,"ds"              );
		colMap.put("sd"               ,"sd"              );
		colMap.put("yardAsgn"         ,"yardAsgn"        );
		colMap.put("yardAsgnSeq"      ,"yardAsgnSeq"     );
		colMap.put("yardAsgnType"     ,"yardAsgnType"    );
		colMap.put("trainId"          ,"trainId"         );
		colMap.put("trnCrewDistr"     ,"trnCrewDistr"    );
		colMap.put("pool"             ,"pool"            );
		colMap.put("finalOs"          ,"finalOs"         );
		colMap.put("cc"               ,"cc"              );
		colMap.put("stateMins"        ,"stateMins"       );
		colMap.put("eahAsgnType"      ,"eahAsgnType"     );
		colMap.put("eahXb"            ,"eahXb"           );
		colMap.put("eahDs"            ,"eahDs"           );
		colMap.put("eahSd"            ,"eahSd"           );
		colMap.put("eahPool"          ,"eahPool"         );
		colMap.put("eahCc"            ,"eahCc"           );
		colMap.put("intermediatePool" ,"intermediatePool");
		colMap.put("achDs"            ,"achDs"           );
		colMap.put("achSd"            ,"achSd"           );
		colMap.put("homeAwayInd"      ,"homeAwayInd"     );
		colMap.put("finalPool"        ,"finalPool"       );
		colMap.put("awayPool"         ,"awayPool"        );
		colMap.put("finalYardAsgn"    ,"finalYardAsgn"   );
		colMap.put("finalVar"         ,"finalVar"        );
		colMap.put("achCC"            ,"achCC"           );
		colMap.put("finalCC"          ,"finalCC"         );
		colMap.put("yardEmp"          ,"yardEmp"         );
		colMap.put("avlUnv"           ,"avlUnv"          );
		return colMap;
	}

	static FluentIterable<Intermediate> sort(final FluentIterable<Intermediate> result) {
		val sorted = result.toSortedList(Comparator.comparing(Intermediate::getEmpNbr).
				thenComparing(Intermediate::getStartTs).
				thenComparing(Intermediate::getEndTs));
		return FluentIterable.from(sorted);
	}

	static boolean isAnyAttendStateW(FluentIterable<Intermediate> intermediates) {
		return stream(intermediates).anyMatch(e -> e
				.getAttendState()
				.equals("W"));
	}
	static boolean isAllAttendStateW(FluentIterable<Intermediate> intermediates){
		return stream(intermediates).allMatch(MarkoffProcessorUtils::isAttendStateW);
	}

}
