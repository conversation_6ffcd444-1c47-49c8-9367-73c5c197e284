package com.nscorp.ccp.logic.markoff.processingSteps;

import com.nscorp.ccp.utils.json.JsonUtils;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.Value;

@NoArgsConstructor(force=true)
@AllArgsConstructor
@Builder(toBuilder = true)
@Value
public class DEmpStatsMedian {
    String ds;
    String sd;
    String finalVar;
    String finalCC;
    Double empTotalStateDays;
    Double empTotalStateDaysMedian;
    Long distinctCntEmp;
    @Override
    public String toString() {
        return JsonUtils.toJsonDebuggingString(this);
    }

}
