package com.nscorp.ccp.logic.markoff.processingSteps;

import com.google.common.collect.FluentIterable;
import com.nscorp.ccp.common.markoff.MarkoffData;
import com.nscorp.ccp.logic.markoff.Intermediate;
import com.nscorp.ccp.utils.lang.Pure;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;

import static com.google.common.collect.Streams.stream;
import static com.nscorp.ccp.logic.markoff.processingSteps.MarkoffProcessorUtils.*;
import static com.nscorp.ccp.utils.logging.LogUtils.info;
import static com.nscorp.ieorcommons.collection.GroupingUtils.groupAndSummarizeToOne;
import static com.nscorp.ieorcommons.collection.GroupingUtils.groupBy;
import static com.nscorp.ieorcommons.collection.NSCollectionUtils.retainFirstOnly;
import static java.util.Comparator.comparing;

@Pure
@Slf4j
public class FinalStepsCalcMarkoffRate {


    @Value
    @Builder
    private static class Key {
        String ds;
        String sd;
        String pool;
        String cc;
        Long distinctCntEmp;
        Double empTotalStateDaysMedian;
    }


    private Key key(final Intermediate m) {
        return Key.builder().
                ds(m.getDs()).
                sd(m.getSd()).
                pool(m.getPool()).
                cc(m.getCc()).
                distinctCntEmp(m.getDistinctCntEmp()).
                empTotalStateDaysMedian(m.getEmpTotalStateDaysMedian()).
                build();
    }
    private Key key(final MarkoffData m) {
        return Key.builder().
                ds(m.getDistr()).
                sd(m.getSubDistr()).
                pool(m.getPoolName()).
                cc(m.getCraft()).
                distinctCntEmp(m.getDistinctCntEmp()).
                empTotalStateDaysMedian(m.getEmpTotalStateDaysMedian()).
                build();
    }

    private FluentIterable<MarkoffData> fillInMissingAvlUnv(FluentIterable<MarkoffData> markoffData){
        val count = markoffData.size();

        if(count == 2){
            return markoffData;
        }

        val m = markoffData.first().orNull();
        if ( m == null ) {
            return FluentIterable.of(MarkoffData.builder().build());
        }

        final MarkoffData toAppend;
        if(m.getAvlUnv().equals("U")){
            toAppend = buildAvlUnvA(m);
        }
        else{
            toAppend = buildAvlUnvU(m);
        }
        return FluentIterable.of(m, toAppend);
    }

    private MarkoffData buildAvlUnvU(final MarkoffData m) {
	    return MarkoffData.builder()
	            .avlUnv("U")
	            .distr(m.getDistr())
	            .subDistr(m.getSubDistr())
	            .poolName(m.getPoolName())
	            .craft(m.getCraft())
	            .distinctCntEmp(m.getDistinctCntEmp())
	            .empTotalStateDaysMedian(m.getEmpTotalStateDaysMedian())
	            .totalStateDays(m.getTotalStateDays())
	            .stateDays(m.getTotalStateDays() - m.getStateDays())
	            .rate(1.0 - m.getRate())
	            .empTotalStateDaysAvg(m.getEmpTotalStateDaysAvg()).build();
    }

    private MarkoffData buildAvlUnvA(final MarkoffData m) {
	    return MarkoffData.builder()
	            .avlUnv("A")
	            .distr(m.getDistr())
	            .subDistr(m.getSubDistr())
	            .poolName(m.getPoolName())
	            .craft(m.getCraft())
	            .distinctCntEmp(m.getDistinctCntEmp())
	            .empTotalStateDaysMedian(m.getEmpTotalStateDaysMedian())
	            .totalStateDays(m.getTotalStateDays())
	            .stateDays(m.getTotalStateDays() - m.getStateDays())
	            .rate(1.0 - m.getRate())
	            .empTotalStateDaysAvg(m.getEmpTotalStateDaysAvg()).build();
    }


    private MarkoffData intermediateToMarkoffData(Intermediate og) {
        return MarkoffData.builder().
                distr(og.getDs()).
                subDistr(og.getSd()).
                poolName(og.getPool()).
                craft(og.getCc()).
                distinctCntEmp(og.getDistinctCntEmp()).
                empTotalStateDaysMedian(og.getEmpTotalStateDaysMedian()).
                totalStateDays(og.getTotalStateDays()).
                stateDays(og.getStateDays()).
                avlUnv(og.getAvlUnv()).
                empTotalStateDaysAvg(og.getEmpTotalStateDaysAvg()).
                rate(og.getRate()).build();
    }

    private FluentIterable<MarkoffData> buildUniqueMarkoffDataFromIntermediates(FluentIterable<Intermediate> intermediates){

        //can also do as above, but add distinct()
        val noDups =  retainFirstOnly(intermediates, this::key);
        return FluentIterable.from(noDups).transform(this::intermediateToMarkoffData);
    }





    // leftJoin(dStatsRates, dEmpStatsFinal);


    public FluentIterable<MarkoffData> finalStepsCalcMarkoffRate(FluentIterable<Intermediate> newStatsWithRenamePoolandCC){
    /*
        This step captures the final step of the Markoff Process and calculates the final Markoff Rates Data
        - Get all unique data ffrom dataset by ds-sd-pool-cc-distinct_cnt_emp-temp_total_state_days-median
        - Merge that unique data with Stats dataset's unique avl_unv data  and call it AllCombo Dataset
        - Full Join AllCombo and Stats Dataset by ds-sd-pool-cc
        - Set final rates and empTotalStateDaysAvg
        - Return final Markoff Data
     */


        //all.combo <- unique(select(d.stats,ds,sd,pool,cc,distinct_cnt_emp, emp_total_state_days_median ))
        val allCombo = buildUniqueMarkoffDataFromIntermediates(newStatsWithRenamePoolandCC);

        //FileUtils.writeCsv(allCombo, "allComboStep1.csv");
        //d stats rates should be spread out bettween U and A avlUNV such that they add up to 100% - or rate = 1
        //all.combo <- merge(all.combo,unique(select(d.stats,avl_unv)))
        //select(d.stats, avl_unv) = select avl_unv from d.stats dataset
        //unique(select(d.stats, avl_unv)) = unique

        val datasetGroupedByAllComboKey = groupBy(allCombo, this::key);
        val allComboFinal = FluentIterable.from(datasetGroupedByAllComboKey.asMap().entrySet())
                .transformAndConcat(mD -> fillInMissingAvlUnv(FluentIterable.from(mD.getValue())));

        //FileUtils.writeCsv(allComboFinal, "allComboFinal.csv");
        info(log, "Final Calculation allCombo data- {}", allComboFinal);
        System.out.println(LocalDateTime.now().format(DTM_FORMATTER) + "Final Calculation allCombo data-: " + stream(allComboFinal).count());

        val dStatsFinalRate = allComboFinal.
                transform(i ->i.withRate(i.getRate() == null ? 0 : i.getRate())).
                transform(i -> i.withEmpTotalStateDaysAvg(i.getTotalStateDays() / i.getDistinctCntEmp().doubleValue()));

        //FileUtils.writeCsv(dStatsFinalRate, "dStatsFinalRate.csv");
        info(log, "Final Calculation dStats- {}", dStatsFinalRate);
        System.out.println(LocalDateTime.now().format(DTM_FORMATTER) + "Final Calculation dStats: " + stream(dStatsFinalRate).count());
        val markoffDataFinal = dStatsFinalRate.
                filter(data -> data.getAvlUnv().equals("U"));

        return markoffDataFinal;
    }
}
