package com.nscorp.ccp.logic.markoff;


import com.fasterxml.jackson.dataformat.csv.CsvMapper;
import com.fasterxml.jackson.dataformat.csv.CsvSchema;
import com.nscorp.ccp.common.markoff.MarkoffInputData;
import com.nscorp.ccp.common.markoff.MarkoffPrt;
import com.nscorp.ccp.utils.lang.Pure;
import io.vavr.control.Try;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

import java.io.*;
import java.util.stream.Collectors;

import static com.google.common.collect.ImmutableList.toImmutableList;

@Pure
@Slf4j
public class ReadCsv {
	@SneakyThrows private static File removeHeaderLine(File file) {
		@Cleanup val reader = new BufferedReader(new FileReader(file));
		val tempFile = File.createTempFile("csv", ".csv");
		@Cleanup val writer = new PrintWriter(new FileWriter(tempFile));
		tempFile.deleteOnExit();
		boolean firstLine = true;
		while ( true ) {
			val line = reader.readLine();
			if ( line == null ) {
				break;
			}
			if ( firstLine ) {
				// Ignore the header line.
				firstLine = false;
			}
			else {
				writer.println(line);
			}
		}
		return tempFile;
	}

	@SneakyThrows public static Iterable<Intermediate> readCsv(final String filename) {

		val result = readCsv(new FileReader(filename));
		return result;
	}
	@SneakyThrows private static Iterable<Intermediate> readCsv(final Reader reader) {

		val schemaFromHeader = CsvSchema.emptySchema().withHeader();
		val result = new CsvMapper().
				readerFor(Intermediate.class).
				with(schemaFromHeader).
				readValues(reader).
				readAll().
				stream().
				map(Intermediate.class::cast).
				collect(toImmutableList());

		return result;
	}

	@SneakyThrows public static Iterable<MarkoffInputData> readMarkoffInputDataCsv(final String filename) {

		val result = readMarkoffInputDataCsv(new FileReader(filename));
		return result;
	}
	@SneakyThrows private static Iterable<MarkoffInputData> readMarkoffInputDataCsv(final Reader reader) {

		val schemaFromHeader = CsvSchema.emptySchema().withHeader();
		val result = new CsvMapper().
				readerFor(MarkoffInputData.class).
				with(schemaFromHeader).
				readValues(reader).
				readAll().
				stream().
				map(MarkoffInputData.class::cast).
				collect(toImmutableList());

		return result;
	}

	@SneakyThrows public static Iterable<MarkoffPrt> readMarkoffPoolInputDataCsv(final String filename) {

		val result = readMarkoffPoolInputDataCsv(new FileReader(filename));
		return result;
	}
	@SneakyThrows private static Iterable<MarkoffPrt> readMarkoffPoolInputDataCsv(final Reader reader) {

		val schemaFromHeader = CsvSchema.emptySchema().withHeader();
		val result = new CsvMapper().
				readerFor(MarkoffPrt.class).
				with(schemaFromHeader).
				readValues(reader).
				readAll().
				stream().
				map(MarkoffPrt.class::cast).
				collect(toImmutableList());

		return result;
	}
}
