package com.nscorp.ccp.logic.markoff.processingSteps;

import com.google.common.collect.FluentIterable;
import com.nscorp.ccp.logic.markoff.Intermediate;
import com.nscorp.ccp.utils.lang.Pure;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.util.List;

import static com.google.common.collect.Streams.stream;
import static com.nscorp.ccp.logic.markoff.processingSteps.MarkoffProcessorUtils.*;
import static com.nscorp.ccp.utils.logging.LogUtils.info;
import static com.nscorp.ieorcommons.collection.GroupingUtils.groupAndSummarizeToOne;
import static com.nscorp.ieorcommons.collection.GroupingUtils.groupBy;

@Pure
@Slf4j
public class CalculateStateDaysAndRates {

    @Value
    @Builder
    private static class FinalKey {
        String ds;
        String sd;
        String finalVar;
        String finalCC;
        Long distinctCntEmp;
        Double stateDays;
        String avlUnv;
    }


    @Value
    @lombok.Builder
    private static class InitialKey {
        String ds;
        String sd;
        String finalVar;
        String finalCC;
    }


    private InitialKey initialKey(final Intermediate i){
        return InitialKey.builder().ds(i.getDs())
                .sd(i.getSd())
                .finalVar(i.getFinalVar())
                .finalCC(i.getFinalCC()).build();
    }


    private FinalKey finalKey(final Intermediate i){
        return FinalKey.builder().ds(i.getDs())
                .sd(i.getSd())
                .finalVar(i.getFinalVar())
                .finalCC(i.getFinalCC())
                .distinctCntEmp(i.getDistinctCntEmp())
                .stateDays(i.getTotalStateDays())
                .avlUnv(i.getAvlUnv()).build();
    }
    private double calculateStateDays(FluentIterable<Intermediate> intermediates){
        return sumWorkTime(intermediates).doubleValue()/60.0/24.0;
    }

    private FluentIterable<Intermediate> calculateEmpCountAndTotalStateDays(FluentIterable<Intermediate> intermediates){
        val empCount = getEmpCount(intermediates);
        val totalStateDays = getTotalStateDays(intermediates);
        return intermediates.
                transform(ent -> ent.withTotalStateDays(totalStateDays).withDistinctCntEmp(empCount));

    }

    private double getTotalStateDays(final FluentIterable<Intermediate> intermediates) {
        return stream(intermediates).mapToDouble(i -> i.getStateMins())
                       .sum() / 60.0 / 24.0;
    }

    private long getEmpCount(final FluentIterable<Intermediate> intermediates) {
        return stream(intermediates).mapToInt(i -> i.getEmpNbr())
                .distinct()
                .count();
    }

    private FluentIterable<Intermediate> setStateDaysAndRates(FluentIterable<Intermediate> intermediateCollection){
        val stateDays = calculateStateDays(intermediateCollection);
        return intermediateCollection.
                transform(i -> i.withStateDays(stateDays).withRate(stateDays/i.getTotalStateDays()));
    }

    public FluentIterable<Intermediate> calculateStateDaysAndRates(FluentIterable<Intermediate> intermediates){
         /*
        This step captures the second part of final step of the Markoff Process and calculates the following:
         - At each ds-sd-final-final_cc-avl_unv: sum of duration in days
           - Step 1: At each ds-sd-final-final_cc: calculate Emp Count And Total State Days
           - Step 2: At Seach ds-sd-final-final_cc-avl_unv: set State Days and Rates
         */


        log.warn(String.format("Starting dStatsStep1. Memory = %d", Runtime.getRuntime().totalMemory()));

        //per  ds,sd,final,final_cc,distinct_cnt_emp,total_state_days,  avl_unv calculate state_days=sum(state_mins)/60/24

        val empStatsFillEmpCntTotalStateDays= FluentIterable.from(groupBy(intermediates, this::initialKey).asMap().entrySet()).
                transformAndConcat(i -> calculateEmpCountAndTotalStateDays(FluentIterable.from(i.getValue())));
        writeIntermediates("dStatsRatesStep1", empStatsFillEmpCntTotalStateDays);

        log.warn(String.format("Starting dStatsRates. Memory = %d", Runtime.getRuntime().totalMemory()));
        val empStatsFillStateDaysAndRates = groupAndSummarizeToOne(empStatsFillEmpCntTotalStateDays, this::finalKey,
                (k,l) -> summarizeIntermediates(l));
        //FileUtils.writeStringToFile("dStatsRatesAdv.txt", dsStatsRatesAdv.toString());
        writeIntermediates("dsStatsRatesFinal", empStatsFillStateDaysAndRates);

        info(log, "Final Calculation dStatsRates- {}", empStatsFillStateDaysAndRates);
        System.out.println(LocalDateTime.now().format(DTM_FORMATTER) + "Final Calculation dStatsRates: " + stream(empStatsFillStateDaysAndRates).count());

        return FluentIterable.from(empStatsFillStateDaysAndRates);
    }

    @NonNull private Intermediate summarizeIntermediates(final List<Intermediate> l) {
        return stream(setStateDaysAndRates(FluentIterable.from(l))).findFirst().orElseThrow();
    }
}
