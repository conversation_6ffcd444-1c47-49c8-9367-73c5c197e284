package com.nscorp.ccp.logic.markoff.processingSteps;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.nscorp.ccp.common.markoff.MarkoffInputData;
import com.nscorp.ccp.utils.json.JsonUtils;
import com.opencsv.bean.CsvBindByName;
import com.opencsv.bean.CsvIgnore;
import lombok.Builder;
import lombok.Value;
import lombok.With;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;


@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
class IntermediateToWrite {
    @CsvBindByName(column="achDs") @JsonProperty("achDs")
    String achDs;
    @CsvBindByName(column="achSd") @JsonProperty("achSd")
    String achSd;
   // @JsonIgnore
    //@CsvIgnore
   @JsonIgnore @CsvIgnore
   @CsvBindByName(column="achCC") @JsonProperty("achCC")
    String achCC;
    @JsonIgnore @CsvIgnore
    String arvlOs;
    @JsonIgnore @CsvIgnore
    Instant asgnTs;

    @CsvBindByName(column="attendState") @JsonProperty("attendState")
    String attendState;
    @CsvBindByName(column="attendSubState") @JsonProperty("attendSubState")
    String attendSubState;

    @JsonIgnore @CsvIgnore
    String avlUnv;

    @CsvBindByName(column="awayPool") @JsonProperty("awayPool")
    String awayPool;

    @CsvBindByName(column="cc") @JsonProperty("cc")
    String cc;

    @JsonIgnore @CsvIgnore
    String cycleTransInd;

    @JsonIgnore @CsvIgnore
    String departOs;

    @JsonIgnore @CsvIgnore
    Long distinctCntEmp;

    @CsvBindByName(column="ds") @JsonProperty("ds")
    String ds;

    @CsvBindByName(column="eahAsgnType") @JsonProperty("eahAsgnType")
    String eahAsgnType;

    @CsvBindByName(column="eahCc") @JsonProperty("eahCc")
    String eahCc;

    @CsvBindByName(column="eahDs") @JsonProperty("eahDs")
    String eahDs;

    @CsvBindByName(column="eahPool") @JsonProperty("eahPool")
    String eahPool;

    @CsvBindByName(column="eahSd") @JsonProperty("eahSd")
    String eahSd;

    @CsvBindByName(column="eahXb") @JsonProperty("eahXb")
    String eahXb;

    @CsvBindByName(column="empNbr") @JsonProperty("empNbr")
    int empNbr;

    @JsonIgnore @CsvIgnore
    Double empTotalStateDays;

    @JsonIgnore @CsvIgnore
    Double empTotalStateDaysAvg;

    @JsonIgnore @CsvIgnore
    Double empTotalStateDaysMedian;

    @CsvBindByName(column="endFuncCd") @JsonProperty("endFuncCd")
    String endFuncCd;

    @CsvBindByName(column="endFuncDesc") @JsonProperty("endFuncDesc")
    String endFuncDesc;

    @JsonIgnore @CsvIgnore
    Instant endStartDeltaDayHourMin;

    @CsvBindByName(column="endSubFuncCd") @JsonProperty("endSubFuncCd")
    String endSubFuncCd;

    @CsvBindByName(column="endSubFuncDesc") @JsonProperty("endSubFuncDesc")
    String endSubFuncDesc;

    @CsvBindByName(column="endTs") @JsonProperty("endTs")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss")
    Instant endTs;

    @JsonIgnore @CsvIgnore
    String finalCC;

    @CsvBindByName(column="finalOs") @JsonProperty("finalOs")
    String finalOs;

    @CsvBindByName(column="finalPool") @JsonProperty("finalPool")
    String finalPool;

   // @JsonIgnore @CsvIgnore
    String finalVar;

    @CsvBindByName(column="finalYardAsgn") @JsonProperty("finalYardAsgn")
    String finalYardAsgn;

    @CsvBindByName(column="homeAwayInd") @JsonProperty("homeAwayInd")
    String homeAwayInd;

    @CsvBindByName(column="intermediatePool") @JsonProperty("intermediatePool")
    String intermediatePool;

    @CsvBindByName(column="loType") @JsonProperty("loType")
    String loType;

    @JsonIgnore @CsvIgnore
    String newDs;

    @JsonIgnore @CsvIgnore
    String newSd;

    @JsonIgnore @CsvIgnore
    Integer originalStateMins;

    @CsvBindByName(column="pool") @JsonProperty("pool")
    String pool;

    @JsonIgnore @CsvIgnore
    Double rate;

    @CsvBindByName(column="rc") @JsonProperty("rc")
    String rc;

    @CsvBindByName(column="sd") @JsonProperty("sd")
    String sd;

    @JsonIgnore @CsvIgnore
    int startDOWSunToSat;

    @CsvBindByName(column="startFuncCd") @JsonProperty("startFuncCd")
    String startFuncCd;

    @CsvBindByName(column="startFuncDesc") @JsonProperty("startFuncDesc")
    String startFuncDesc;

    @CsvBindByName(column="startSubFuncCd") @JsonProperty("startSubFuncCd")
    String startSubFuncCd;

    @CsvBindByName(column="startSubFuncDesc") @JsonProperty("startSubFuncDesc")
    String startSubFuncDesc;

    @CsvBindByName(column="startTs") @JsonProperty("startTs")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss")
    Instant startTs;

    @JsonIgnore @CsvIgnore
    Double stateDays;

    @CsvBindByName(column="stateMins") @JsonProperty("stateMins")
    Integer stateMins;

    @JsonIgnore @CsvIgnore
    Double totalStateDays;

    @CsvBindByName(column="trainId") @JsonProperty("trainId")
    String trainId;

    @CsvBindByName(column="trnCrewDistr") @JsonProperty("trnCrewDistr")
    String trnCrewDistr;

    @JsonIgnore @CsvIgnore
    String trnOrgnDay;

    @CsvBindByName(column="yardAsgn") @JsonProperty("yardAsgn")
    String yardAsgn;

    @CsvBindByName(column="yardAsgnSeq") @JsonProperty("yardAsgnSeq")
    String yardAsgnSeq;

    @CsvBindByName(column="yardAsgnType") @JsonProperty("yardAsgnType")
    String yardAsgnType;

    @JsonIgnore @CsvIgnore
    Boolean yardEmp;
    @Override
    public String toString() {
        return JsonUtils.toJsonDebuggingString(this);
    }
}
