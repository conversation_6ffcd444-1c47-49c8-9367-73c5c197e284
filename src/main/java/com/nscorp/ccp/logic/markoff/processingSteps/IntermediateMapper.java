package com.nscorp.ccp.logic.markoff.processingSteps;

import com.nscorp.ccp.common.markoff.MarkoffInputData;
import com.nscorp.ccp.logic.markoff.Intermediate;
import com.nscorp.ccp.logic.markoff.processingSteps.IntermediateToWrite;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface IntermediateMapper {
	Intermediate toIntermediate(MarkoffInputData markoffInputData);
	IntermediateToWrite toIntermediateToWrite(Intermediate intermediate);
}
