package com.nscorp.ccp.logic.markoff;


import com.google.common.collect.FluentIterable;
import com.google.common.collect.Iterables;
import com.nscorp.ccp.utils.lang.Pure;

import java.util.Comparator;

import static com.nscorp.ieorcommons.lang.NSStringUtils.blankToNull;

@Pure
public class IntermediateReformatter {
	public Iterable<Intermediate> reformat(Iterable<Intermediate> input) {
		return FluentIterable.from(input).
				transform(e->e.withFinalVar(blankToNull(e.getFinalVar()))).
				toSortedList(Comparator.comparing(Intermediate::getEmpNbr).
						thenComparing(Intermediate::getStartTs).
						thenComparing(Intermediate::getAchCC));
	}
}
