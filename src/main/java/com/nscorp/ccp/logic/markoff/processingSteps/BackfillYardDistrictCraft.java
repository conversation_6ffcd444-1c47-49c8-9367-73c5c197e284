package com.nscorp.ccp.logic.markoff.processingSteps;

import com.google.common.collect.*;
import com.nscorp.ccp.logic.markoff.Intermediate;
import com.nscorp.ccp.utils.lang.Pure;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.lang3.StringUtils;

import java.time.format.DateTimeFormatter;

import static com.nscorp.ccp.logic.markoff.processingSteps.MarkoffProcessorUtils.*;
import static io.vavr.Function0.constant;
import static org.apache.commons.lang3.StringUtils.*;

@Pure
@Slf4j
public class BackfillYardDistrictCraft {
    private static final DateTimeFormatter DTM_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");

    private FluentIterable<Intermediate> populateFinalYardAsgn(final FluentIterable<Intermediate> intermediates){
        //This step is concerned with setting the Final yard assignment variable of each row within each group of employment IDs
        //Final Yard Asgn = itself unless Previous row's finalYardAsgn if Deadhead and finalYardAsgn is blank
         //first step we need to remove all non-qualifying work events (with attendState != W or startFuncCd = 35)
        final FluentIterable<Intermediate> result;
        val removeNonWorkEventsIdx = Iterables.indexOf(intermediates, this::isWorkEventValid);
        if(removeNonWorkEventsIdx >= 0){
            //we throw away any non-valid records before the first work event
            val validIntermediatesList = ImmutableList.copyOf(intermediates);
            val validIntermediates = FluentIterable.from(validIntermediatesList.subList(removeNonWorkEventsIdx, validIntermediatesList.size()));
            val idx = Iterables.indexOf(validIntermediates, this::isFinalYardAsgnValid);

            if ( idx >= 0 ) {
                // Match found.
                val list = ImmutableList.copyOf(validIntermediates);
                // Split at predicate.  Before does *not* have the element matching the predicate, and after *does* have it.
                val before = FluentIterable.from(list.subList(0, idx));
                val after = FluentIterable.from(list.subList(idx, list.size()));

                // Take the element matching the predicate and get its finalYardAsgn.
                val finalYardAsgn = after.
                        first().
                        toJavaUtil().
                        map(Intermediate::getFinalYardAsgn).
                        orElseThrow();

                // Copy 'finalYardAsgn' to all elements in the 'before' list.
                val beforeWithFinalYardAsgn = before.transform(i -> i.withFinalYardAsgn(finalYardAsgn));
                val newIntermediatesWithFinalYardAsgn = backPopulateFinalYardAsgn(finalYardAsgn, after);

                result = beforeWithFinalYardAsgn.append(newIntermediatesWithFinalYardAsgn);
            }
            else {
                // No match found.
                result = validIntermediates;
            }
        }
        else {
            // No match found.
            result = FluentIterable.of();
        }


        return result;
    }

    private FluentIterable<Intermediate> backPopulateFinalYardAsgn(String finalYardAsgn, FluentIterable<Intermediate> intermediates) {
        return backPopulateField(finalYardAsgn, intermediates, this::isFinalYardAsgnValid, Intermediate::getFinalYardAsgn, Intermediate::withFinalYardAsgn);
    }
    private boolean isWorkEventValid(Intermediate intermediate){
        //startFuncCd =35 = deadhead
        //if this current record is a deadhead,and the Final Yard Asignment variable is blank for this row,
        //set the finalYardAsignment = the previous row's final yard assignment
        //otherwise keep the final yard assignment
        return isAttendStateW(intermediate) && ! isStartFunc35(intermediate);
    }

    private boolean isFinalYardAsgnValid(Intermediate intermediate){
        //startFuncCd =35 = deadhead
        //if this current record is a deadhead,and the Final Yard Asignment variable is blank for this row,
        //set the finalYardAsignment = the previous row's final yard assignment
        //otherwise keep the final yard assignment
        return isWorkEventValid(intermediate) ||
               StringUtils.isNotEmpty(intermediate.getFinalYardAsgn());
    }


    private FluentIterable<Intermediate> populateDsSdFinalVar(FluentIterable<Intermediate> intermediates){

        //in this step we first set the FinalVar variable (named Final in the document) to the finalPool Variable as set from step 3
        val setFinalVarList = intermediates.
                transform(i -> i.withFinalVar(i.getFinalPool()));

        // Extract finalVar from first element matching predicate 'isStep5FinalPoolSingleValid', or use "" if none found.
        val firstMatch = setFinalVarList.filter(this::isValid).first().toJavaUtil();

        val finalVar = firstMatch.map(Intermediate::getFinalVar).orElse("");
        val ds = firstMatch.map(Intermediate::getDs).orElse("");
        val sd = firstMatch.map(Intermediate::getSd).orElse("");

        return constant(setFinalVarList).
                andThen(i-> backPopulateDs(ds, i)).
                andThen(i-> backPopulateSd(sd, i)).
                andThen(i-> backPopulateFinalVar(finalVar, i)).
                apply();
    }

    private FluentIterable<Intermediate> backPopulateDs(String initialDs, FluentIterable<Intermediate> intermediates) {
        return backPopulateField(initialDs, intermediates, this::isValid, Intermediate::getDs, Intermediate::withDs);
    }

    private FluentIterable<Intermediate> backPopulateSd(String initialSd, FluentIterable<Intermediate> intermediates) {
        return backPopulateField(initialSd, intermediates, this::isValid, Intermediate::getSd, Intermediate::withSd);
    }

    private FluentIterable<Intermediate> backPopulateFinalVar(String initialFinalVar, FluentIterable<Intermediate> intermediates) {
        return backPopulateField(initialFinalVar, intermediates, this::isValid, Intermediate::getFinalVar, Intermediate::withFinalVar);
    }

    private boolean isValid(Intermediate intermediate){
        return isWorkEventValid(intermediate) ||
              StringUtils.isNotEmpty(intermediate.getFinalVar()) ||
              StringUtils.isNotEmpty(intermediate.getFinalYardAsgn());
    }

    private FluentIterable<Intermediate> populateAchCC(FluentIterable<Intermediate> intermediates){
        /*
                The goal is to have a craft code for each row when filter out conductor trainee working in extraboard.
                We find the first non-null craft code, and then set all achCC to that value
                add a new column ach_cc  initialize with this row’s cc column  and then take the missing ones from pre row ach_cc per emp_nbr

                If still missing, take ach_cc from next row per emp_nbr.
             */
        //add a new column ach_cc, initialize with this row’s cc column,
        // 	and then take the missing ones from pre row ach_cc per emp_nbr
        val cc = intermediates.
                filter(i -> StringUtils.isNotEmpty(i.getCc())).
                first().
                toJavaUtil().
                map(Intermediate::getCc).
                orElse("");

        val intermediatesWithAchCCForward = backPopulateAchCC(cc, intermediates);

        //if still missing
        val stillMissingAchCC = intermediatesWithAchCCForward.filter(i -> StringUtils.isEmpty(i.getAchCC()));
        final FluentIterable<Intermediate> result;
        if(stillMissingAchCC.isEmpty()){
            result = intermediatesWithAchCCForward;
        }
        else{
            // Get first nonempty CC in reverse order, or "" if none is found.
            val intermediatesToReverse = reverseList(intermediatesWithAchCCForward);
            val revCc = intermediatesToReverse.
                    filter(i -> StringUtils.isNotEmpty(i.getCc())).
                    first().
                    toJavaUtil().
                    map(Intermediate::getCc).
                    orElse("");

            //if still missing, take ach_cc from next row per emp_nbr.
            val intermediatesWithAchCCBackwards = backPopulateAchCC(revCc, intermediatesToReverse);
	        result = reverseList(intermediatesWithAchCCBackwards);
        }
        return result;
    }


    private FluentIterable<Intermediate> backPopulateAchCC(String AchCC, FluentIterable<Intermediate> intermediates) {
        return backPopulateField(AchCC, intermediates,
                i -> StringUtils.isNotEmpty(i.getCc()), Intermediate::getCc, Intermediate::withAchCC);
    }

    private String getFinalCC(Intermediate intermediate){
        final String result;
        if(isEahAsgnTypeX((intermediate)))	{
            val achCC = intermediate.getAchCC();
            if (startsWithAny(intermediate.getEahXb(), "C", "B", "S")
                    &&
                    intermediate.getAchCC() != null &&  !equalsAny(achCC, "CT", "CX")) {

                result = "CO";

            } else if (startsWith(intermediate.getEahXb(), "E")) {

                result = "EN";

            } else {
                result = "Non-CO/EN";
            }
        }else{
            result = intermediate.getCc();
        }
        return result;
    }

    private FluentIterable<Intermediate> backPopulateFinalCC(String finalCC,
                                                             FluentIterable<Intermediate> intermediates) {
        return backPopulateField(finalCC, intermediates,
                i -> StringUtils.isNotEmpty(i.getFinalCC()), Intermediate::getFinalCC, Intermediate::withFinalCC);
    }

    private FluentIterable<Intermediate> populateFinalCC(FluentIterable<Intermediate> intermediates){
		/*
			Take finalCC from previous & subsequent row per emp_nbr :
			        - First, fill in finalCC from same row
			        - Next forward  populate finalCC based on previous row
			        - If there are still rows w/o finalCC, then backwards populate finalCC based on next row
		 */
        val intWithFinalCC = intermediates.transform(
                i -> i.withFinalCC(getFinalCC(i))
        );
        val cc = intWithFinalCC.
                filter(i -> StringUtils.isNotEmpty(i.getFinalCC())).
                first().
                toJavaUtil().
                map(Intermediate::getFinalCC).
                orElse("");

        val intermediatesWithFinalCCForward = backPopulateFinalCC(cc, intWithFinalCC);
        val stillMissingFinalCC = intermediatesWithFinalCCForward.filter(i -> StringUtils.isEmpty(i.getFinalCC()));

        final FluentIterable<Intermediate> result;
        if(stillMissingFinalCC.isEmpty()){
            result = intermediatesWithFinalCCForward;
        }
        else{
            val intermediatesToReverse = reverseList(intermediatesWithFinalCCForward);

            val revCc = intermediatesToReverse.
                    filter(i -> StringUtils.isNotEmpty(i.getFinalCC())).
                    first().
                    toJavaUtil().
                    map(Intermediate::getFinalCC).
                    orElse("");
            val intermediatesWithFinalCCBackwards = backPopulateFinalCC(revCc, intermediatesToReverse);

	        result =  reverseList(intermediatesWithFinalCCBackwards);
        }
        return result;
    }
    private FluentIterable<Intermediate> processOneEmp(FluentIterable<Intermediate> intermediates) {
        return constant(intermediates).
                andThen(this::populateFinalYardAsgn).
                andThen(e->writeIntermediates("PostStep4", e)).
                andThen(this::populateDsSdFinalVar).
                andThen(e->writeIntermediates("PostStep5", e)).
                andThen(this::populateAchCC).
                andThen(e->writeIntermediates("PostStep6", e)).
                andThen(this::populateFinalCC).
                andThen(e->writeIntermediates("PostStep7", e)).
                apply();
    }
     public FluentIterable<Intermediate> backfillYardDistrictCraft(
                final FluentIterable<Intermediate> intermediates){

         /*
            This class captures Steps 4 - 7 of the Markoff Process
            - Take yard assign from previous row per emp_nbr (but also remove all non-qualifying work events )
            - Forward Populate DS, SD, Pool from previous home pool per emp_nbr
            - Take cc from previous & subsequent row per emp_nbr (populating AchCC as well)
            - Take final_cc from previous & subsequent row per emp_nbr for populating generic craft code
             */

         return groupByEmpNbr(intermediates).
                 transformAndConcat(this::processOneEmp);
         /*
         writeIntermediates("PostStep4", step4WithFinalYardAsgnList);

         System.out.println(LocalDateTime.now().format(DTM_FORMATTER) + "Step 4  take yard assign from previous row per emp_nbr Count: " + stream(step4WithFinalYardAsgnList).count());
         log.warn(String.format("Step 4. Memory = %d", Runtime.getRuntime().totalMemory()));
         //Step 5: take pool from previous home pool per emp_nbr

         val step5WithDsSdFinalVarList = mapByEmps(step4WithFinalYardAsgnList).
                 transform(Tuple2::_2).
                 transformAndConcat(ent -> populateDsSdFinalVar(FluentIterable.from(ent)) );

         writeIntermediates("PostStep5", step5WithDsSdFinalVarList);

         //loadAndCompare(step5List, "after_step5.csv");

         System.out.println(LocalDateTime.now().format(DTM_FORMATTER) + "Step 5 take pool from previous home pool per emp_nbr Count: " + stream(step5WithDsSdFinalVarList).count());
         log.warn(String.format("Step 5. Memory = %d", Runtime.getRuntime().totalMemory()));
         //step 6: take cc from previous & subsequent row per emp_nbr

         val step6WithCCAchCCList = mapByEmps(step5WithDsSdFinalVarList).
                 transform(Tuple2::_2).
                 transformAndConcat(values -> populateAchCC(FluentIterable.from(values)));

         writeIntermediates("PostStep6", step6WithCCAchCCList);
         //val step6LoadedData = loadAndCompareReturnLoadData(step6WithCCAchCCList, "after_step6.csv");
         //writeCsv( FluentIterable.from(step6LoadedData).filter(i -> i.getEmpNbr() == 1191 ||  i.getEmpNbr() == 3648), "RDataStep6EmpFilter.csv");
         //writeCsv(step6WithCCAchCCList.filter(i -> i.getEmpNbr() == 1191 ||  i.getEmpNbr() == 3648), "JavaDataStep6EmpFilter.csv" );
         System.out.println(LocalDateTime.now().format(DTM_FORMATTER) + "Step 6 take cc from previous & subsequent row per emp_nbr Count: " + stream(step6WithCCAchCCList).count());
         log.warn(String.format("Step 6. Memory = %d", Runtime.getRuntime().totalMemory()));
         //step 7: take final_cc from previous & subsequent row per emp_nbr

         val step7WithFinalCCList = mapByEmps(step6WithCCAchCCList).
                 transform(Tuple2::_2).
                 transformAndConcat(values -> populateFinalCC(FluentIterable.from(values)));

         System.out.println(LocalDateTime.now().format(DTM_FORMATTER) + "Step 7: take final_cc from previous & subsequent row per emp_nbr Count: " + stream(step7WithFinalCCList).count());
         log.warn(String.format("Step 7. Memory = %d", Runtime.getRuntime().totalMemory()));

            return step7WithFinalCCList;
         */
     }

}
