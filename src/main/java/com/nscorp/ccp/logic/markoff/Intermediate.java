package com.nscorp.ccp.logic.markoff;

import com.fasterxml.jackson.annotation.*;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.nscorp.ccp.common.markoff.MarkoffInputData;
import com.nscorp.ccp.utils.json.JsonUtils;
import com.opencsv.bean.CsvBindByName;
import lombok.*;

import java.time.Instant;
import java.time.LocalDateTime;


@Value
@Builder(toBuilder = true)
@With
@AllArgsConstructor
@NoArgsConstructor(force = true)
@JsonIgnoreProperties({"train_id"})
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({"achDs","achSd","attendState","attendSubState","awayPool","cc","ds","eahAsgnType","eahCc","eahDs","eahPool","eahSd","eahXb","empNbr","endFuncCd","endFuncDesc","endSubFuncCd","endSubFuncDesc","endTs","finalOs","finalPool","finalVar","finalYardAsgn","homeAwayInd","intermediatePool","loType","pool","rc","sd","startFuncCd","startFuncDesc","startSubFuncCd","startSubFuncDesc","startTs","stateMins","trainId","trnCrewDistr","yardAsgn","yardAsgnSeq","yardAsgnType"})
public class Intermediate {

    @CsvBindByName(column="empNbr") @JsonProperty("empNbr")
    int empNbr;

    @CsvBindByName(column="attendState") @JsonProperty("attendState")
    String attendState;

    @CsvBindByName(column="attendSubState") @JsonProperty("attendSubState")
    String attendSubState;

    @CsvBindByName(column="startTs") @JsonProperty("startTs")
    @JsonDeserialize(using = SafeLocalDateTimeDeserializer.class)
    Instant startTs;

    @CsvBindByName(column="startFuncCd") @JsonProperty("startFuncCd")
    String startFuncCd;

    @CsvBindByName(column="startFuncDesc") @JsonProperty("startFuncDesc")
    String startFuncDesc;

    @CsvBindByName(column="startSubFuncCd") @JsonProperty("startSubFuncCd")
    String startSubFuncCd;

    @CsvBindByName(column="startSubFuncDesc") @JsonProperty("startSubFuncDesc")
    String startSubFuncDesc;

    @CsvBindByName(column="endTs") @JsonProperty("endTs")
    @JsonDeserialize(using = SafeLocalDateTimeDeserializer.class)
    Instant endTs;

    @CsvBindByName(column="endFuncCd") @JsonProperty("endFuncCd")
    String endFuncCd;

    @CsvBindByName(column="endFuncDesc") @JsonProperty("endFuncDesc")
    String endFuncDesc;

    @CsvBindByName(column="endSubFuncCd") @JsonProperty("endSubFuncCd")
    String endSubFuncCd;

    @CsvBindByName(column="endSubFuncDesc") @JsonProperty("endSubFuncDesc")
    String endSubFuncDesc;

    @CsvBindByName(column="rc") @JsonProperty("rc")
    String rc;

    @CsvBindByName(column="loType") @JsonProperty("loType")
    String loType;

    @CsvBindByName(column="ds") @JsonProperty("ds")
    String ds;

    @CsvBindByName(column="sd") @JsonProperty("sd")
    String sd;

    @CsvBindByName(column="yardAsgn") @JsonProperty("yardAsgn")
    String yardAsgn;

    @CsvBindByName(column="yardAsgnSeq") @JsonProperty("yardAsgnSeq")
    String yardAsgnSeq;

    @CsvBindByName(column="yardAsgnType") @JsonProperty("yardAsgnType")
    String yardAsgnType;

    @CsvBindByName(column="trainId") @JsonProperty("trainId")
    String trainId;

    @CsvBindByName(column="trnCrewDistr") @JsonProperty("trnCrewDistr")
    String trnCrewDistr;

    @CsvBindByName(column="pool") @JsonProperty("pool")
    String pool;

    //@CsvBindByName(column="text") @JsonProperty("departOs")
    // String departOs;

    ////@CsvBindByName(column="text") @JsonProperty("arvl_os")
    //String arvlOs;

    @CsvBindByName(column="finalOs") @JsonProperty("finalOs")
    String finalOs;

    @CsvBindByName(column="cc") @JsonProperty("cc")
    String cc;

    @CsvBindByName(column="stateMins") @JsonProperty("stateMins")
    Integer stateMins;

    @CsvBindByName(column="eahAsgnType") @JsonProperty("eahAsgnType")
    String eahAsgnType;

    @CsvBindByName(column="eahXb") @JsonProperty("eahXb")
    String eahXb;

    @CsvBindByName(column="eahDs") @JsonProperty("eahDs")
    String eahDs;

    @CsvBindByName(column="eahSd") @JsonProperty("eahSd")
    String eahSd;

    @CsvBindByName(column="eahPool") @JsonProperty("eahPool")
    String eahPool;

    @CsvBindByName(column="eahCc") @JsonProperty("eahCc")
    String eahCc;

    @CsvBindByName(column="intermediatePool") @JsonProperty("intermediatePool")
    String intermediatePool;

    @CsvBindByName(column="achDs") @JsonProperty("achDs")
    String achDs;

    @CsvBindByName(column="achSd") @JsonProperty("achSd")
    String achSd;

    @CsvBindByName(column="homeAwayInd") @JsonProperty("homeAwayInd")
    String homeAwayInd;

    @CsvBindByName(column="finalPool") @JsonProperty("finalPool")
    String finalPool;

    @CsvBindByName(column="awayPool") @JsonProperty("awayPool")
    String awayPool;

    @CsvBindByName(column="finalYardAsgn") @JsonProperty("finalYardAsgn")
    String finalYardAsgn;

    @CsvBindByName(column="finalVar") @JsonProperty("finalVar")
    String finalVar;

    // //@CsvBindByName(column="text") @JsonProperty("emp_nbr")
    // String newDs;

    // //@CsvBindByName(column="text") @JsonProperty("emp_nbr")
    // String newSd;

    @CsvBindByName(column="achCC") @JsonProperty("achCC")
    String achCC;

    @CsvBindByName(column="finalCC") @JsonProperty("finalCC")
    String finalCC;

    @CsvBindByName(column="yardEmp") @JsonProperty("yardEmp")
    Boolean yardEmp;

    @CsvBindByName(column="avlUnv") @JsonProperty("avlUnv")
    String avlUnv;
    Integer originalStateMins;
    Double empTotalStateDays;
    Double totalStateDays;
    Double empTotalStateDaysMedian;
    Double stateDays;
    Long distinctCntEmp;
    Double rate;
    Double empTotalStateDaysAvg;

    @Override
    public String toString() {
        return JsonUtils.toJsonDebuggingString(this);
    }
}
