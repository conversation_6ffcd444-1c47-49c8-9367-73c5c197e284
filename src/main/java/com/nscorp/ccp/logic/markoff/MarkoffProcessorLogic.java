package com.nscorp.ccp.logic.markoff;

import com.google.common.collect.FluentIterable;
import com.nscorp.ccp.common.markoff.*;
import com.nscorp.ccp.logic.markoff.processingSteps.*;
import com.nscorp.ccp.utils.io.FileUtils;
import com.nscorp.ccp.utils.lang.Pure;
import lombok.extern.slf4j.Slf4j;
import lombok.val;

import java.io.File;
import java.io.IOException;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.*;

import static com.google.common.collect.ImmutableMap.toImmutableMap;
import static com.google.common.collect.Streams.stream;
import static com.nscorp.ccp.logic.markoff.processingSteps.MarkoffProcessorUtils.*;
import static com.nscorp.ccp.utils.json.JsonUtils.OBJECT_MAPPER;
import static com.nscorp.ccp.utils.logging.LogUtils.info;

import static java.util.Comparator.comparing;
import static org.apache.commons.lang3.StringUtils.*;

@Slf4j
@Pure
public class MarkoffProcessorLogic {
	private static final DateTimeFormatter DTM_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");

	private String getIntermediatePool(MarkoffInputData mData) {

		final String result;
		if(equalsAny(mData.getEahAsgnType(), "X")){
			result = mData.getEahXb();
		}
		else{
			result = mData.getPool();
		}
		return result;
	}

	private String getIntermediatePoolFromIntermediate(Intermediate mData) {
		final String result;
		if(equalsAny(mData.getEahAsgnType(), "X")){
			result = mData.getEahXb();
		}
		else{
			result = mData.getPool();
		}
		return result;
	}


	private Intermediate createIntermediateData(final MarkoffInputData mData) {
		//if eah_asgn
		val intermediatePool = getIntermediatePool(mData);
		return MAPPER.toIntermediate(mData).withIntermediatePool(intermediatePool);
	}

	private Intermediate createIntermediateDataFromInputFile(final Intermediate iData) {
		//if eah_asgn
		val intermediatePool = getIntermediatePoolFromIntermediate(iData);
		return iData.withIntermediatePool(intermediatePool);
	}

	private FluentIterable<MarkoffInputData> standardSort(final FluentIterable<MarkoffInputData> result) {
		val sorted = result.toSortedList(Comparator.comparing(MarkoffInputData::getEmpNbr).
				thenComparing(MarkoffInputData::getStartTs).
				thenComparing(MarkoffInputData::getEndTs));
		return FluentIterable.from(sorted);
	}

	public Iterable<MarkoffData> process(Iterable<MarkoffInputData> markoffInputDataParam,
										 final Iterable<MarkoffPrt> poolData,
										 LocalDate endDate,
										 Integer studyLengthMonths) {

		val markoffInputData = standardSort(FluentIterable.from(markoffInputDataParam));

		//markoffInputData comes in as an Ordered list
		//use the last day of the previous month from the end date as the true start Date, then subtract 6 months
		val trueStartDate = endDate.withDayOfMonth(1).minusMonths(studyLengthMonths);//removing minusDays(1) b/c we only use this to keep data after the start of the day
		val refDate = endDate.withDayOfMonth(1);
		log.warn("trueStartDate: " + trueStartDate);
		log.warn("Ref Date: " + refDate);

		//write Markoff Input Data to CSV for re-use of dataset
		//	val step1ToTest = FluentIterable.from(markoffInputData).transform( mD -> MAPPER.toIntermediate(mD));
		//writeCsv(step1ToTest,  "markoffInputData.csv");
		//FileUtils.writeCsv(markoffInputData, "markoffInputData.csv");

		//step 3: set final_pool, final_yard_asgn directly from the same row
		val markoffDataWithIntermediatePool = markoffInputData.
				transform(this::createIntermediateData);
		//writeIntermediates("PostStep1", markoffDataWithIntermediatePool);
		//loadAndCompare(markoffDataWithIntermediatePool, "after_step1.csv");


		log.warn(String.format("Starting Step 2 and 3 : MergeWithPoolProfileYardAsignSteps. Memory = %d", Runtime.getRuntime().totalMemory()));
		val intermediatesWithPoolDataAndFinalYardAsign = new MergeWithPoolProfileYardAsignSteps().
																mergeWithPoolProfileYardAsignSteps(FluentIterable.from(poolData), markoffDataWithIntermediatePool);


		log.warn(String.format("Starting Step 4-7 : initialPerEmpStepsBackfillYardDistrictCraft. Memory = %d", Runtime.getRuntime().totalMemory()));
		val intermediatesWithBackfilledYardDistrictCraft = new BackfillYardDistrictCraft().
				backfillYardDistrictCraft(intermediatesWithPoolDataAndFinalYardAsign);

		val forwardPopulated = new ForwardPopulateDistrictPool().backfillDistrictCraftPool(intermediatesWithBackfilledYardDistrictCraft);

		log.warn(String.format("Starting Step 8-9 : finalPerEmpStepsBackfillDistrictCraftPool. Memory = %d", Runtime.getRuntime().totalMemory()));
		val intermediatesFinalBackfilledDistrictCraftPool = new BackfillDistrictCraftPool().
				backfillDistrictCraftPool(forwardPopulated);
		//writeIntermediates("step 9 Final Data", intermediatesFinalBackfilledDistrictCraftPool);


		log.warn(String.format("Starting Step 10 : step10FilterEmpsAtPoolLvl. Memory = %d", Runtime.getRuntime().totalMemory()));
		//step 10: Calculation of markoff rate
		val step10InitialFilter  = new FilterEmpsAtPoolLevel().filterEmpsAtPoolLevel(intermediatesFinalBackfilledDistrictCraftPool, trueStartDate);

		val step10FinalFilter = new RegroupEmpsAndRemoveInvalidRecords().regroupEmpsAndRemoveInvalidRecords(step10InitialFilter, endDate, studyLengthMonths);

		val empStatsFillEmpTotalStateDaysMedian = new CalculateDistinctEmpCntAndMedianStateDays().calculateDistinctEmpCntAndMedianStateDays(step10FinalFilter);

		val empStatsFillStateDaysAndRates = new CalculateStateDaysAndRates().calculateStateDaysAndRates(step10FinalFilter);

		val leftJoinedDataWithFilledPoolCC = new LeftJoinStateDaysRatsStatsAndEmpCntMedian().leftJoinStateDaysRatsStatsAndEmpCntMedian(
				empStatsFillStateDaysAndRates, empStatsFillEmpTotalStateDaysMedian
		);
		//next we need to group by DS, SD, FinalVar, and FinalCC and then apply filtering
		log.warn(String.format("Starting Final Step : finalStepsCalcMarkoffRate. Memory = %d", Runtime.getRuntime().totalMemory()));
		val markoffDataFinal = new FinalStepsCalcMarkoffRate().finalStepsCalcMarkoffRate(leftJoinedDataWithFilledPoolCC);
		System.out.println(LocalDateTime.now().format(DTM_FORMATTER) + "markoff Data Final: " + stream(markoffDataFinal).count() + " rows");
		//group by DS, SD, Final, FinalCC

		writeFinalMarkoffData(markoffDataFinal);
		return markoffDataFinal;
	}

}
