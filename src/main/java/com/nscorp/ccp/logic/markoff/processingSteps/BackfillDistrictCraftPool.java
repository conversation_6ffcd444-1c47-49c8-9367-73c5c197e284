package com.nscorp.ccp.logic.markoff.processingSteps;


import com.google.common.collect.FluentIterable;
import com.nscorp.ccp.logic.markoff.Intermediate;
import com.nscorp.ccp.utils.lang.Pure;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.lang3.StringUtils;

import static com.nscorp.ccp.logic.markoff.processingSteps.MarkoffProcessorUtils.*;
import static io.vavr.Function0.constant;
import static org.apache.commons.lang3.StringUtils.*;
import static org.apache.commons.lang3.StringUtils.equalsAny;

@Pure
@Slf4j
public class BackfillDistrictCraftPool {

	public FluentIterable<Intermediate> backfillDistrictCraftPool(
			final FluentIterable<Intermediate> intermediates) {

		//step 9: still miss pool, take pool from subsequent row per emp_nbr
		val result = groupByEmpNbr(intermediates).
				transformAndConcat(this::backfillForOneEmp);
		writeIntermediates("PostStep9", result);
		//loadAndCompare(step9List, "after_step9.csv");
		log.warn(String.format("Step 9. Memory = %d", Runtime.getRuntime().totalMemory()));
		return result;
	}

	private FluentIterable<Intermediate> backPopulateDs(String ds,
															 FluentIterable<Intermediate> intermediates){
		return backPopulateField(ds, intermediates, this::isDsValid, Intermediate::getDs, Intermediate::withDs);
	}
	private FluentIterable<Intermediate> backPopulateSd(String sd,
															 FluentIterable<Intermediate> intermediates){
		return backPopulateField(sd, intermediates, this::isSdValid, Intermediate::getSd, Intermediate::withSd);
	}
	private FluentIterable<Intermediate> backpopulateFinalVar(String finalVar,
																   FluentIterable<Intermediate> intermediates){
		return backPopulateField(finalVar, intermediates, this::isFinalVarValid, Intermediate::getFinalVar, Intermediate::withFinalVar);
	}

	private FluentIterable<Intermediate> backfillForOneEmp(FluentIterable<Intermediate> intermediates){
		val intermediatesReversed = reverseList(intermediates);
		val ds = intermediatesReversed.filter(this::isDsValid).first().toJavaUtil().map(Intermediate::getDs).orElse("");
		val sd = intermediatesReversed.filter(this::isSdValid).first().toJavaUtil().map(Intermediate::getSd).orElse("");
		val finalVar = intermediatesReversed.filter(this::isFinalVarValid).first().toJavaUtil().map(Intermediate::getFinalVar).orElse("");
		//should I reverse the search for ds, sd, finalVar too?
		//The step to reverse to get the above vars isn't in the documentation

		val newIntermediates =  constant(intermediatesReversed).
				andThen(i -> backPopulateDs(ds, i)).
				andThen(i -> backPopulateSd(sd, i)).
				andThen(i -> backpopulateFinalVar(finalVar, i)).apply();

		return reverseList(newIntermediates);
	}

	private boolean isDsValid(Intermediate intermediate){
		return equalsAny(intermediate.getAttendState(), "W")//, "T")
		   || isNotEmpty(intermediate.getFinalVar())
		   || isNotEmpty(intermediate.getFinalYardAsgn()) ;
	}

	private boolean isSdValid(Intermediate intermediate){
		return equalsAny(intermediate.getAttendState(), "W")//, "T")
		       || isNotEmpty(intermediate.getFinalVar())
		       || isNotEmpty(intermediate.getFinalYardAsgn());
	}
	private boolean isFinalVarValid(Intermediate intermediate){
		return equalsAny(intermediate.getAttendState(), "W", "T")
		   || isNotEmpty(intermediate.getFinalVar())
		   || isNotEmpty(intermediate.getFinalYardAsgn()) ;
	}
}
