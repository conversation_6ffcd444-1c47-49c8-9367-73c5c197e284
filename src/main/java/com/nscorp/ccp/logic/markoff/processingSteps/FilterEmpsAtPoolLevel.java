package com.nscorp.ccp.logic.markoff.processingSteps;


import com.google.common.collect.FluentIterable;
import com.nscorp.ccp.logic.markoff.Intermediate;
import com.nscorp.ccp.utils.lang.Pure;
import io.vavr.Tuple2;
import lombok.extern.slf4j.Slf4j;
import lombok.val;

import java.time.LocalDate;
import java.time.ZoneId;

import static com.google.common.collect.Streams.stream;
import static com.nscorp.ccp.logic.markoff.processingSteps.MarkoffProcessorUtils.*;
import static com.nscorp.ieorcommons.time.NSDateUtils.localDateToInstant;
import static org.apache.commons.lang3.StringUtils.*;

@Pure
@Slf4j
public
/**
 *
 */
class FilterEmpsAtPoolLevel {

	private static final int FOUR_MONTH_LAYOFF_THRESHOLD = 4 * 30 * 24 * 60;
	private static final int DISMISSED_TERMINATED_TWENTY_DAY_THRESHOLD = 20 * 24 * 60;
	private boolean isFinalYardAsgnValid(Intermediate intermediate){
		//set to true if not null and length > 0
		//set to false if null or length == 0
		return isNotEmpty(intermediate.getFinalYardAsgn());
	}

	private String getAvlUnv(Intermediate intermediate){
		final String result;

		if(equalsAny(intermediate.getAttendState(), "A", "W")
		   		|| equalsAny(intermediate.getLoType(), "%", "Y")){
			result = "A";
		}else if(equalsAny(intermediate.getAttendState(), "T", "L")){
			result = "U";
		}else{
			result = "unknown";
		}
		return result;
	}

	private Integer getStateMins(Intermediate intermediate){
		final Integer result;
		if(equalsAny(intermediate.getLoType(), "%", "Y")){
			result = 0;
		}else{
			result = intermediate.getStateMins();
		}
		return result;
	}
	private Intermediate fillYardEmpAvlUnvStateMins(final Intermediate intermediate) {
		return intermediate.
				withYardEmp(isFinalYardAsgnValid(intermediate)).
				withAvlUnv(getAvlUnv(intermediate)).
				withOriginalStateMins(intermediate.getStateMins()).
				withStateMins(getStateMins(intermediate));
	}

	private boolean isValidPoolEmployeeForTimeRange(Intermediate intermediate, LocalDate trueStartDate) {
		//filter out employees by time range and if they are pool employees (not yard employees)
		val trueStartStartOfDay = localDateToInstant(trueStartDate, ZoneId.systemDefault());
		return intermediate.getStartTs().isAfter(trueStartStartOfDay)
		       && !intermediate.getYardEmp() //yardEmp value is true
		       && equalsAny(intermediate.getFinalCC(), "EN", "CO")
		       && isNotEmpty(intermediate.getFinalVar());  //had greater than 1, which is wierd
	}

	private boolean isValidDismissal(Intermediate e){
		return(e.getEndSubFuncDesc() != null &&
				!(e.getEndSubFuncDesc().equals("Dismissed") ||
						e.getEndSubFuncDesc().equals("Terminate Record")) )
				||
              e.getStateMins() < DISMISSED_TERMINATED_TWENTY_DAY_THRESHOLD;
	}
	private boolean isValidTrainee(Intermediate e){
		val eahXb = e.getEahXb();
		val achCC = e.getAchCC();
		val eahAsgnType = e.getEahAsgnType();
		return isValidEahAsgnType(eahAsgnType) || isValidEahXb(eahXb) || isValidAchCC(achCC);
	}

	private boolean isValidEahAsgnType(String eahAsgnType) {
		return eahAsgnType == null || ! eahAsgnType.equals("X");
	}

	private boolean isValidAchCC(String achCC) {
		return achCC != null && ! equalsAny(achCC, "CT", "CX");
	}

	private boolean isValidEahXb(final String eahXb) {
		return !startsWithAny(eahXb, "B", "S", "C");
	}

	private boolean isValidLayoff(Intermediate e){
		return !(
				equalsAny(e.getAttendState(), "L", "T")
						&& e.getStateMins() > FOUR_MONTH_LAYOFF_THRESHOLD);//layoff filter set to 4 months

	}
	private FluentIterable<Intermediate> filterByAttendState(final FluentIterable<Intermediate> transformed) {
		return
				// Group by empNbr
				groupByEmpNbr(transformed).

				// Require at least one element with attendState='W'
				filter(MarkoffProcessorUtils::isAnyAttendStateW).

				// Flatmap and return.
				transformAndConcat(ent->ent);
	}
	private FluentIterable<Intermediate> eliminateDiscontinuousRecords(final FluentIterable<Intermediate> intermediates) {
		val pairs = pairs(intermediates). //step10_sorted_by_empNbr_startTS_endTSpairMap(intermediate i1, intermediate i2 -> return Tuple.of(i1, i2)) returns collection of tuples of adjacent objects
				filter(tup-> ! isTimeDiffNegative(tup)).
				transform(Tuple2::_2);
		return intermediates.first().toJavaUtil().
				// Prepend the first element.
				map(f->FluentIterable.of(f).append(pairs)).

				// No first element, just return the pairs.
				orElse(pairs);
	}
	public FluentIterable<Intermediate> filterEmpsAtPoolLevel(final FluentIterable<Intermediate> intermediates, final LocalDate trueStartDate) {
		/*
			This step is captures the first part of Step 10 of the Markoff Process and involves the following sub-steps
			- Fill yardEmp, avlUnv, and calculate stateMins
			- Filter by time range and pool employees (not yard employees)
			- Filter by attendState (only consider employees that have at least 1 work record attendState=W)
			- Filter out Layoff and displacement rows longer than 4 months
			- Eliminate trainee from extraboard
			- Eliminate dismissed and terminated employees previous longer than 20 days record
			- Sort the records by empNbr, startTS, endTs
			- Eliminate discontinuous record: (this row start_ts != previous row end_ts)
		 */
		val transformed = intermediates.
				transform(this::fillYardEmpAvlUnvStateMins).
				filter(intermediate -> isValidPoolEmployeeForTimeRange(intermediate, trueStartDate));

		val filteredByAttendStateLayoffTraineeDismissed = filterByAttendState(transformed).
				filter(this::isValidLayoff).
				filter(this::isValidTrainee).
				filter(this::isValidDismissal);

		//System.out.println(LocalDateTime.now().format(DTM_FORMATTER) + "Step 10: step10_sorted_by_empNbr_startTS_endTS: " + sorted.size());
		val result = eliminateDiscontinuousRecords(sort(filteredByAttendStateLayoffTraineeDismissed));
		//writeIntermediates("Step 10 Final", finalized);

		return result;
	}

}
