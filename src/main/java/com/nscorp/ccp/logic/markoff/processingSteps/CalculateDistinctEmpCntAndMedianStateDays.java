package com.nscorp.ccp.logic.markoff.processingSteps;

import com.google.common.collect.FluentIterable;
import com.nscorp.ccp.logic.markoff.Intermediate;
import com.nscorp.ccp.utils.json.JsonUtils;
import com.nscorp.ccp.utils.lang.Pure;
import io.vavr.Tuple2;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

import java.util.function.Function;

import static com.google.common.collect.Streams.stream;
import static com.nscorp.ccp.logic.markoff.processingSteps.MarkoffProcessorUtils.indexByKey;
import static com.nscorp.ccp.logic.markoff.processingSteps.MarkoffProcessorUtils.sumWorkTime;
import static com.nscorp.ieorcommons.collection.GroupingUtils.*;
import static com.nscorp.ieorcommons.collection.NSCollectionUtils.retainFirstOnly;
import static java.util.Comparator.comparing;

@Pure
@Slf4j
public class CalculateDistinctEmpCntAndMedianStateDays {
    @Value
    @Builder
    private static class Key {
        String ds;
        String sd;
        String finalVar;
        String finalCC;
        Integer empNbr;
    }
    @Builder(toBuilder = true)
    @Value
    @With
    private static class DEmpStatsTotalStateDays {
        String ds;
        String sd;
        String finalVar;
        String finalCC;
        int empNbr;
        double empTotalStateDays;
        //double empTotalStateDaysMedian;


        @Override
        public String toString() {
            return JsonUtils.toJsonDebuggingString(this);
        }
    }

    private FinalKey finalKey(DEmpStatsTotalStateDays i) {
        return FinalKey.builder().ds(i.getDs()).sd(i.getSd()).finalVar(i.getFinalVar()).finalCC(i.getFinalCC()).build();
    }

    private Key key(DEmpStatsTotalStateDays i) {
        return Key.builder().ds(i.getDs()).sd(i.getSd()).finalVar(i.getFinalVar()).finalCC(i.getFinalCC()).empNbr(i.getEmpNbr()).build();
    }

    @Value
    @Builder
    private static class FinalKey {
        String ds;
        String sd;
        String finalVar;
        String finalCC;
    }

    private FluentIterable<Tuple2<Key, FluentIterable<Intermediate>>> groupByKey(FluentIterable<Intermediate> intermediates){
        return indexByKey(intermediates, this::key);
    }

    private Key key(final Intermediate i) {
        return Key.builder()
                .ds(i.getDs())
                .sd(i.getSd())
                .finalVar(i.getFinalVar())
                .finalCC(i.getFinalCC())
                .empNbr(i.getEmpNbr())
                .build();
    }

    private <T> Double computeMedian(final FluentIterable<T> ts, Function<T,Double> extractor) {
        val sorted = ts.toSortedList(comparing(extractor));
        var count = sorted.size();
        return count % 2 == 0 ? (extractor.apply(sorted.get(count / 2 - 1)) + extractor.apply(sorted.get(count / 2))) / 2.0 : extractor.apply(sorted.get(count / 2));
    }

    public FluentIterable<DEmpStatsMedian> calculateDistinctEmpCntAndMedianStateDays(final FluentIterable<Intermediate> intermediates){

         /*
        This step captures the first part final step of the Markoff Process and calculates the following:
        - At each ds-sd-final-final_cc: get distinct individual employees count & sum of duration for all employees
            - Step 1: At each ds-sd-final-final_cc-emp_nbr: get total duration days
            - Step 2: At each ds-sd-final-final_cc: get median of EMP_TOTAL_STATE_DAYS
        */

        val empStatsFillTotalDurationDays = groupByKey(intermediates).
                transform(Tuple2::_2).
                transformAndConcat(intermediateCollection -> {
                    return intermediateCollection.
                            transform(i -> buildDEmpStatsTotalStateDays(intermediateCollection, i));
                });

        val empStatsFillTotalDurationDaysFilteredToOneRecordEach = retainFirstOnly(empStatsFillTotalDurationDays, this::key);

        //FileUtils.writeCsv(empStatsFillTotalDurationDaysFilteredToOneRecordEach, "dEmpStatsStep1.csv");
        log.warn(String.format("Starting dEmpStatsFinal. Memory = %d", Runtime.getRuntime().totalMemory()));

        val empStatsFillEmpTotalStateDaysMedian = groupAndSummarizeToOne(empStatsFillTotalDurationDaysFilteredToOneRecordEach,
                this::finalKey,
                (k,l)-> buildDEmpStatsMedian(k, FluentIterable.from(l)));

        //FileUtils.writeCsv(empStatsFillEmpTotalStateDaysMedian, "dEmpStatsFinal.csv");
        return FluentIterable.from(empStatsFillEmpTotalStateDaysMedian);
    }

    private DEmpStatsMedian buildDEmpStatsMedian(final FinalKey key, final FluentIterable<DEmpStatsTotalStateDays> l) {
        return DEmpStatsMedian.builder().
                ds(key.getDs()).
                sd(key.getSd()).
                finalVar(key.getFinalVar()).
                finalCC(key.getFinalCC()).
                distinctCntEmp(getDistinctCntEmps(l)).
                empTotalStateDaysMedian(getEmpTotalStateDaysMedian(l)).
                build();
    }

    private Double getEmpTotalStateDaysMedian(final FluentIterable<DEmpStatsTotalStateDays> l) {
        return computeMedian(l, DEmpStatsTotalStateDays::getEmpTotalStateDays);
    }

    private long getDistinctCntEmps(final FluentIterable<DEmpStatsTotalStateDays> l) {
        return stream(l).mapToInt(DEmpStatsTotalStateDays::getEmpNbr)
                .distinct()
                .count();
    }

    private DEmpStatsTotalStateDays buildDEmpStatsTotalStateDays(final FluentIterable<Intermediate> intermediateCollection, final Intermediate i) {
        return DEmpStatsTotalStateDays.builder().
                ds(i.getDs()).
                sd(i.getSd()).
                finalVar(i.getFinalVar()).
                finalCC(i.getFinalCC()).
                empNbr(i.getEmpNbr()).
                empTotalStateDays(getEmpTotalStateDays(intermediateCollection))
                .build();
    }

    private double getEmpTotalStateDays(final FluentIterable<Intermediate> intermediateCollection) {
        return sumWorkTime(intermediateCollection).doubleValue() / 60.0 / 24.0;
    }
}
