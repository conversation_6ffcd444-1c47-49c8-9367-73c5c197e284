package com.nscorp.ccp.logic.markoff.processingSteps;


import com.google.common.collect.FluentIterable;
import com.nscorp.ccp.logic.markoff.Intermediate;
import com.nscorp.ccp.utils.lang.Pure;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.jetbrains.annotations.NotNull;

import java.time.LocalDateTime;

import static com.google.common.collect.Streams.stream;
import static com.nscorp.ccp.logic.markoff.processingSteps.MarkoffProcessorUtils.*;
import static io.vavr.Function0.constant;
import static org.apache.commons.lang3.StringUtils.*;

@Pure
@Slf4j
public class ForwardPopulateDistrictPool {
	public FluentIterable<Intermediate> backfillDistrictCraftPool(
			final FluentIterable<Intermediate> intermediates) {

		//writeIntermediates("dg_loc0", intermediates);
		//step 8: still miss pool use away pool take from previous row per emp_nbr
		val result = groupByEmpNbr(intermediates).
				transformAndConcat(this::backFillForOneEmp);
		//loadAndCompare(step8List, "after_step8.csv");

		System.out.println(LocalDateTime.now()
				                   .format(DTM_FORMATTER) + "Step 8: still miss pool use away pool take from previous row per emp_nbr Count: " + stream(result).count());
		log.warn(String.format(
				"Step 8. Memory = %d", Runtime.getRuntime()
						.totalMemory()));

		writeIntermediates("PostStep8", result);
		return result;
	}
	private FluentIterable<Intermediate> backFillForOneEmp(FluentIterable<Intermediate> intermediates){

		val intermediatesWithFinalVar = intermediates.
				transform(intermediate -> intermediate.withFinalVar(getFinalVar(intermediate)));

		val match = intermediatesWithFinalVar.filter(this::isValid)
				.first()
				.toJavaUtil();
		val ds = match.map(Intermediate::getDs).orElse("");
		val sd = match.map(Intermediate::getSd).orElse("");
		val finalVar = match.map(Intermediate::getFinalVar).orElse("");

		return constant(intermediatesWithFinalVar).
				andThen(i -> backPopulateDs(ds, i)).
				andThen(i -> backPopulateSd(sd, i)).
				andThen(i -> backPopulateFinalVar(finalVar, i)).apply();
	}

	private FluentIterable<Intermediate> backPopulateDs(String ds,
			FluentIterable<Intermediate> intermediates){
		return backPopulateField(ds, intermediates, this::isValid, Intermediate::getDs, Intermediate::withDs);
	}
	private FluentIterable<Intermediate> backPopulateSd(String sd,
			FluentIterable<Intermediate> intermediates){
		return backPopulateField(sd, intermediates, this::isValid, Intermediate::getSd, Intermediate::withSd);
	}
	private FluentIterable<Intermediate> backPopulateFinalVar(String finalVar,
			FluentIterable<Intermediate> intermediates){
		return backPopulateField(finalVar, intermediates, this::isValid, Intermediate::getFinalVar, Intermediate::withFinalVar);
	}

	private boolean isValid(Intermediate intermediate){
		return (equalsAny(intermediate.getAttendState(), "W", "T")
		    && !isStartFunc35(intermediate))
		       || isNotEmpty(intermediate.getFinalVar())
		       || isNotEmpty(intermediate.getFinalYardAsgn());
	}
	private String getFinalVar(Intermediate intermediate){
		return isEmpty(intermediate.getFinalVar()) ? intermediate.getAwayPool() : intermediate.getFinalVar();
	}
}
