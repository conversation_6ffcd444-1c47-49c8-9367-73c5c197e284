package com.nscorp.ccp.logic.markoff.processingSteps;

import com.nscorp.ccp.utils.json.JsonUtils;
import lombok.Builder;
import lombok.Value;
import lombok.With;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
@NoArgsConstructor(force=true)
@AllArgsConstructor
@Builder(toBuilder = true)
@Value
@With
public class DEmpStatsTotalStateDays {
    String ds;
    String sd;
    String finalVar;
    String finalCC;
    String key;
    String finalKey;
    int empNbr;
    double empTotalStateDays;
    //double empTotalStateDaysMedian;
    @Override
    public String toString() {
        return JsonUtils.toJsonDebuggingString(this);
    }

}
