package com.nscorp.ccp.logic.markoff.processingSteps;

import com.google.common.collect.FluentIterable;
import com.nscorp.ccp.logic.markoff.Intermediate;
import com.nscorp.ccp.utils.lang.Pure;
import io.vavr.Tuple2;
import lombok.Value;
import lombok.extern.slf4j.Slf4j;
import lombok.val;

import java.time.*;
import java.util.Set;

import static com.google.common.collect.Streams.stream;
import static com.nscorp.ccp.logic.markoff.processingSteps.MarkoffProcessorUtils.*;
import static com.nscorp.ccp.utils.date.DateTimeUtils.*;

@Pure
@Slf4j
public class RegroupEmpsAndRemoveInvalidRecords {
    @Value
    @lombok.Builder
    private static class PoolKey {
        String ds;
        String sd;
        String finalVar;
        String finalCC;
    }
    private FluentIterable<Tuple2<PoolKey, FluentIterable<Intermediate>>> groupByPoolKey(FluentIterable<Intermediate> intermediates){
        //maps key to collection of intermediates (b/c multiple Intermediates can be assigned to a key and that makes a collection)
        return indexByKey(intermediates, i ->
                PoolKey.builder().ds(i.getDs())
                        .sd(i.getSd())
                        .finalVar(i.getFinalVar())
                        .finalCC(i.getFinalCC()).build());

    }

    private boolean isTotalWorkTimeAtLeast12Hours(FluentIterable<Intermediate> intermediates){
        //choose to filter IN those records by emp whose work time >= 12 hours
        //return (sumWorkTime(intermediates)/60) >= 368;  //(368 = 8*(52-4-2))
        return ((double)sumWorkTime(intermediates)/60.0) >= 12.0;
    }

    private boolean hasWorkedRecentlyBasedOnStartTs(Intermediate intermediate, LocalDate refDate){
        //refDate is last Date of previous month
        //if(intermediate.getAttendState().equals("W") && intermediate.getStartTs().isAfter(refDate.minusDays(62).atStartOfDay())){
        val ld = instantToLocalDate(intermediate.getStartTs());
        return ld.isAfter(refDate.minusDays(62)); //refDate.minusDays(62).atStartOfDay(eastern).toInstant())
    }


    private boolean hasWorkedRecentlyBasedOnEndTs(Intermediate intermediate, LocalDate refDate){
        //refDate is last Date of previous month
        // Keep records where ATTEND_STATE = W (W = work event) and the event is within the last 2 months
        //filter(all(end_dt<= ref.date - days(62) ) )   # past 2 months
        //if(intermediate.getAttendState().equals("W") && intermediate.getEndTs().isBefore(refDate.minusDays(62).atStartOfDay())){
        val refDateMinusTwoMonths = refDate.minusDays(62);
        val ld = instantToLocalDate(intermediate.getEndTs());
        return ld.isAfter(refDateMinusTwoMonths); //refDate.minusDays(62).atStartOfDay(eastern).toInstant())
    }
    private boolean isWorkSumDurationAtLeastThreshold(FluentIterable<Intermediate> intermediates,
                                          Integer studyLengthMonths){
        //return boolean to specify if we should filter this record IN based on total work time < threshold time
        //because we want to keep emps that have lots of records and filter out emps with few records
        val intermediatesWithoutFilter = intermediates.filter(i -> i.getAttendState().equals("W"));
        val thresholdVal = studyLengthMonths >= 6 ?  8.0*(52.0/12.0 * (double) studyLengthMonths - 6.0 ) : 8.0*((52.0/12.0) * ((double)studyLengthMonths - 4.0));
        return (sumWorkTime(intermediatesWithoutFilter)/60) >= thresholdVal;
    }

    private int sumWorkTime(FluentIterable<Intermediate> intermediates){
        return stream(intermediates).mapToInt(Intermediate::getStateMins).sum();
    }

    public FluentIterable<Intermediate> regroupEmpsAndRemoveInvalidRecords(
            final FluentIterable<Intermediate> intermediates,
 LocalDate endDate,
             int studyLengthMonths){
        /*
        This step is captures the final part of Step 10 of the Markoff Process and involves regrouping the emp data
         by DS, SD, finalVar, finalCC and then filtering out unreasonable or invalid data
         - Map emp records by DS, SD, FinalVar, FinalCC
         - Filter out records that only have Work events for n months (all records in grouping have attendState=W)
         - Filter out records that have no Work events for n months (no records in grouping have attendStat = W)
         - Filter out records where the Work total duration is short
         - Filter out inactive pools - no work events for last 2 months
         - Filter out inactive pools - work total duration for last 2 months is under 12 hours
         - Return pool of Interest
         */
        val refDate = endDate.withDayOfMonth(1);
        System.out.println("REF DATE MINUS 62 days: " + refDate.minusDays(62).atStartOfDay());

        val interestingRecords = getInterestingRecords(intermediates, studyLengthMonths);

        //only keep records(by DS,SD,Final,finalCC) where we have data in the last 2 months
        val deadPools = getKeysOfNoneWorkedRecently(interestingRecords, refDate);

        val interestingByPool = groupByPoolKey(interestingRecords);
        val result = interestingByPool.transformAndConcat(tup -> {
                val retain = retainPool(tup._1, tup._2, deadPools, refDate);
                return retain ? tup._2 : FluentIterable.of();
            });

        // val recordsOfPoolsOfInterest = getFinalPoolsOfInterest(interestingRecords, deadPools, refDate);

        // info(log, "Step 10: Pool of Int - {}", poolOfInt);
        //System.out.println(LocalDateTime.now().format(DTM_FORMATTER) + "Step 10: Pool of Int Count: " + stream(poolOfInt).count());
        writeIntermediates("AfterStep10PoolOfInt", result);
        log.warn(String.format("Starting dEmpStatsStep1. Memory = %d", Runtime.getRuntime().totalMemory()));

        return result;
    }

    private boolean retainPool(
            final PoolKey poolKey,
            final FluentIterable<Intermediate> recordsForThisPool,
            final Set<PoolKey> deadPools,
            final LocalDate refDate) {
        if ( deadPools.contains(poolKey) ) {
            // Pool is dead.  Nothing to do.
            return false;
        }

        // Start with interestingRecords and filter out those with pools that have no recent work.
        val workedRecentlyBasedOnStartTs = recordsForThisPool.
                filter(MarkoffProcessorUtils::isAttendStateW).
                filter(i -> hasWorkedRecentlyBasedOnStartTs(i, refDate));

        val workedLessThan12Hours = ! isTotalWorkTimeAtLeast12Hours(workedRecentlyBasedOnStartTs);
        if ( workedLessThan12Hours ) {
            // Pool is uninteresting.  Nothing to do.
            return false;
        }

        return true;
    }

    private FluentIterable<Intermediate> getInterestingRecords(final FluentIterable<Intermediate> intermediates, final int studyLengthMonths) {
	    return groupByPoolKey(intermediates).
	            transform(Tuple2::_2).
	            filter(ent -> ! MarkoffProcessorUtils.isAllAttendStateW(ent)). //filter only keeps those where not all rows are W
	            filter(MarkoffProcessorUtils::isAnyAttendStateW). //filter only keeps those that have at least 1 W
	            filter(ent -> isWorkSumDurationAtLeastThreshold(ent, studyLengthMonths)).
	            transformAndConcat(ent -> ent);
    }

    private Set<PoolKey> getKeysOfNoneWorkedRecently(final FluentIterable<Intermediate> interestingRecords, final LocalDate refDate) {
        val goodAttendState = interestingRecords.filter(MarkoffProcessorUtils::isAttendStateW);
        val noneWorkedRecently = groupByPoolKey(goodAttendState).
                filter(ent -> haveNoneWorkedRecently(ent._2, refDate)) ;
        return noneWorkedRecently.transform(Tuple2::_1).toSet();
    }

    private boolean haveNoneWorkedRecently(final FluentIterable<Intermediate> ent, final LocalDate refDate) {
        return stream(ent).noneMatch(emp -> hasWorkedRecentlyBasedOnEndTs(emp, refDate));
    }
}
