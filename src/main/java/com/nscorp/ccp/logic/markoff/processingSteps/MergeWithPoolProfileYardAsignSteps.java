package com.nscorp.ccp.logic.markoff.processingSteps;

import com.google.common.collect.FluentIterable;
import com.nscorp.ccp.common.markoff.MarkoffPrt;
import com.nscorp.ccp.logic.markoff.Intermediate;
import com.nscorp.ccp.utils.lang.Pure;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import javax.validation.constraints.NotNull;
import java.time.format.DateTimeFormatter;
import java.util.Map;

import static com.google.common.collect.ImmutableMap.toImmutableMap;
import static com.google.common.collect.Streams.stream;
import static com.nscorp.ccp.logic.markoff.processingSteps.MarkoffProcessorUtils.*;
import static java.util.function.Function.identity;
import static org.apache.commons.lang3.StringUtils.isNotEmpty;

@Pure
@Slf4j
public class MergeWithPoolProfileYardAsignSteps {
    private static final DateTimeFormatter DTM_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");

    @Value
    @Builder
    private static class PoolProfileKey {
        String ds;
        String sd;
        String os;
        String pool;
    }

    private PoolProfileKey getPrtPoolProfileKey(MarkoffPrt prt) {
        return new PoolProfileKey(
                prt.getDs(),
                prt.getSd(),
                prt.getOs(),
                prt.getPool());
    }



    private String getHomeAwayInd(MarkoffPrt poolData) {
        return poolData == null ? null : poolData.getHomeAwayInd();
    }


    private PoolProfileKey getPoolFromIntermediateKey(Intermediate intermediate) {
        return new PoolProfileKey(
                intermediate.getDs(),
                intermediate.getSd(),
                intermediate.getFinalOs(),
                intermediate.getIntermediatePool()) ;
    }

    private Map<PoolProfileKey,MarkoffPrt> mapPools(Iterable<MarkoffPrt> poolData) {
        return stream(poolData).
                collect(toImmutableMap(this::getPrtPoolProfileKey, identity())); //toMap(Key is Method Reference to make prtPoolKey from MarkoffPrt Object, identity() returns the MarkoffPrt object itself)
    }

    private String getAwayPool(Intermediate intermediate) {
        final String result;
        if(isNotEmpty(intermediate.getIntermediatePool())
           && (StringUtils.equals(intermediate.getHomeAwayInd(), "A") ||
               isEahAsgnTypeX(intermediate))
        ){
            result = intermediate.getIntermediatePool();
        }
        else{
            result = null;//result = "NoAwayPool";
        }
        return result;
    }

    private String getFinalYardAsgn(Intermediate intermediate) {
        final String result;

        if(StringUtils.equals(intermediate.getEahAsgnType(), "X")){
            //means extra board
            result = "";
        }
        else{
            result = intermediate.getYardAsgn();
        }
        return result;
    }

    private String getFinalPool(Intermediate intermediate) {
        final String result;
        if(isNotEmpty(intermediate.getIntermediatePool())
           && (StringUtils.equals(intermediate.getHomeAwayInd(), "H") ||
                StringUtils.equals(intermediate.getEahAsgnType(), "X"))
        ){

            result = intermediate.getIntermediatePool();
        }
        else{
            result = null;
        }
        return result;
    }
    private String getDs(Intermediate intermediate) {
        return StringUtils.equals(intermediate.getEahAsgnType(), "X") ?
                intermediate.getEahDs() : intermediate.getAchDs();
    }

    private String getSd(Intermediate intermediate) {
        return StringUtils.equals(intermediate.getEahAsgnType(), "X") ?
                intermediate.getEahSd() : intermediate.getAchSd();
    }

    public @NotNull FluentIterable<Intermediate> mergeWithPoolProfileYardAsignSteps(
            final FluentIterable<MarkoffPrt> poolData,
            final FluentIterable<Intermediate> intermediates){
        /*
        This class captures Steps 2 and 3 of the Markoff Process
        - Map Markoff Pool Profile data from Teradata by DS, SD, OS, POOL key
        - Per intermediate from Markoff inout data, set achDS,  achSD, DS, SD directly from the same row
        - Join Markoff Pool Profile data to Markoff Inout data by DS, SD, FinalOS, IntermediatePool to get homeAwayInd
        - Set finalPool, awayPool, finalYardAsgn directly from the same row
         */
        val poolsByKey = mapPools(poolData);
        //writeCsv(poolData, "Step2PoolData.csv");
        return 	intermediates.transform(intermediate -> intermediate.withAchDs(intermediate.getDs())).
                transform(intermediate -> intermediate.withAchSd(intermediate.getSd())).
                transform(intermediate->intermediate.withDs(getDs(intermediate))).
                transform(intermediate->intermediate.withSd(getSd(intermediate))).
                transform(intermediate -> {
                    //Create poolKey to search by ds, sd, os, pool
                    //Set Home Away Indicator from pool data by this key
                    val pData = poolsByKey.get(getPoolFromIntermediateKey(intermediate));
                    return intermediate.withHomeAwayInd(getHomeAwayInd(pData));
                }).
                transform(intermediate -> intermediate.withFinalPool(getFinalPool(intermediate))).
                transform(intermediate -> intermediate.withAwayPool(getAwayPool(intermediate))).
                transform(intermediate -> intermediate.withFinalYardAsgn(getFinalYardAsgn(intermediate)));


/*
        //writeIntermediates("PostStep3", step3List);
        System.out.println(LocalDateTime.now().format(DTM_FORMATTER) + "Step 3 Intermediates Count: " + stream(intermediates).count());
        log.warn(String.format("Step 1 - Teradata - {}. Memory = %d", Runtime.getRuntime().totalMemory()));

        System.out.println(LocalDateTime.now().format(DTM_FORMATTER) + "Step 3 Pool Data Count: " + stream(poolData).count());
*/

    }
}
