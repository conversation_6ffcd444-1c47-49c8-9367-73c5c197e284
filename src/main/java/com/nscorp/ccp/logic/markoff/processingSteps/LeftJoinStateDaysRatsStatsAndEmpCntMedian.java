package com.nscorp.ccp.logic.markoff.processingSteps;

import com.google.common.collect.FluentIterable;
import com.nscorp.ccp.logic.markoff.Intermediate;
import com.nscorp.ccp.utils.lang.Pure;
import lombok.Value;
import lombok.extern.slf4j.Slf4j;
import lombok.val;

import java.time.LocalDateTime;
import java.util.Optional;

import static com.google.common.collect.Streams.stream;
import static com.nscorp.ccp.logic.markoff.processingSteps.MarkoffProcessorUtils.DTM_FORMATTER;
import static com.nscorp.ccp.logic.markoff.processingSteps.MarkoffProcessorUtils.writeIntermediates;
import static com.nscorp.ieorcommons.collection.JoinUtils.*;

@Pure
@Slf4j
public class LeftJoinStateDaysRatsStatsAndEmpCntMedian {
    @Value
    @lombok.Builder
    private static class Key {
        String ds;
        String sd;
        String finalVar;
        String finalCC;
    }

    private Key key(Intermediate i) {
        return Key.builder().ds(i.getDs())
                .sd(i.getSd())
                .finalVar(i.getFinalVar())
                .finalCC(i.getFinalCC())
                .build();
    }

    private Key key(DEmpStatsMedian median) {
        return Key.builder().ds(median.getDs())
                .sd(median.getSd())
                .finalVar(median.getFinalVar())
                .finalCC(median.getFinalCC())
                .build();
    }

    private FluentIterable<Intermediate> leftJoin(FluentIterable<Intermediate> leftList, FluentIterable<DEmpStatsMedian> rightList){
        //FluentIterable.from(leftList).uniqueIndex(
        System.gc();
        log.warn(String.format("Starting Leftjoin. Memory = %d", Runtime.getRuntime().totalMemory()));
        // left outer join d.stats and d.stats.emp - perform a natural join,
        // using all variables in common across d.stats and d.stats.emp
        //join by ds,sd,final,final_cc

        val joinResult = leftOuterJoinNoDups(leftList, rightList,
                (l,r)-> r.map(right-> consolidate(l, right)).
                        or(()-> Optional.of(consolidateMissingRightElement(l))),
                this::key,this::key);

        return FluentIterable.from(joinResult);
    }

    private Intermediate consolidateMissingRightElement(final Intermediate l) {
        return l.withEmpTotalStateDaysMedian(0.0)
                .withEmpTotalStateDays(0.0);
    }

    private Intermediate consolidate(final Intermediate l, final DEmpStatsMedian right) {
        return l.withDs(right.getDs())
                .withSd(right.getSd())
                .withFinalVar(right.getFinalVar())
                .withFinalCC(right.getFinalCC())
                .withEmpTotalStateDaysMedian(right.getEmpTotalStateDaysMedian())
                .withEmpTotalStateDays(right.getEmpTotalStateDays())
                .withDistinctCntEmp(right.getDistinctCntEmp());
    }


    public FluentIterable<Intermediate> leftJoinStateDaysRatsStatsAndEmpCntMedian(
            FluentIterable<Intermediate> empStatsFillStateDaysAndRates,
            FluentIterable<DEmpStatsMedian> empStatsFillEmpTotalStateDaysMedian
    ){
        /*
        This step captures the third part of the final step of the Markoff Process and calculates the final Markoff Rates Data

        - Left Outer Join both above datasets by ds-sd-final-final_cc
        - Rename finalVar to pool
        - Rename finalCC to CC
        - This gives us our Stats dataset

     */
        // left outer join d.stats and d.stats.emp - perform a natural join,
        // using all variables in common across d.stats and d.stats.emp
        //join by ds,sd,final,final_cc
        val joinedStats = leftJoin(empStatsFillStateDaysAndRates, empStatsFillEmpTotalStateDaysMedian);
        writeIntermediates("leftJoin", joinedStats);

        //FileUtils.writeStringToFile("Post Left Join.txt", joinedStats.toString());

        //after left join, dStatsRates row count should be the same as before the join
        System.out.println(LocalDateTime.now().format(DTM_FORMATTER) + "Final Calculation joinedStats: " + stream(joinedStats).count());
        val newStatsWithRenamePoolandCC = joinedStats.
                transform(i -> i.
                        withPool(i.getFinalVar()).
                        withCc(i.getFinalCC()));

        writeIntermediates("newDStatsPostLeftJoin", newStatsWithRenamePoolandCC);

        return newStatsWithRenamePoolandCC;
    }
}
