package com.nscorp.ccp;

import com.google.common.collect.Lists;
import com.nscorp.ccp.config.CcpCoreConfiguration;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.*;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import java.util.Properties;

import static com.nscorp.ccp.utils.logging.LogUtils.warn;

@SpringBootApplication(scanBasePackages = {"com.nscorp.ccp"})
@Import({CcpCoreConfiguration.class})
@PropertySource("classpath:core.properties")

// Use CGLIB-based proxying for transaction management.
@EnableTransactionManagement(mode=AdviceMode.PROXY, proxyTargetClass = true)
@Slf4j
@RequiredArgsConstructor
public class Launcher {

    /**
     * @param argv The arguments.
     */
    @SneakyThrows public static void main(final String[] argv) {
        val argList = Lists.newArrayList(argv);
        if ( argList.size() < 1 ) {
            System.err.println("At least 1 argument is required.");
            Runtime.getRuntime().halt(1);
        }
        val cmd = argList.remove(0);
        val defaultProperties = new Properties();

        // If we are not running the service, disable the embedded app server.
        if ( ! cmd.equals("svc") ) {
            warn(log, "Disabling embedded app server.");
            defaultProperties.setProperty("spring.main.web-application-type", "none");
        }

        // If we are not running the shell, disable it.
        if ( ! cmd.equals("shell") ) {
            warn(log, "Disabling Spring Shell.");
            defaultProperties.setProperty("spring.shell.interactive.enabled", "false");
            defaultProperties.setProperty("spring.shell.history.enabled", "false");
        }

        val app = new SpringApplication(Launcher.class);
        app.setDefaultProperties(defaultProperties);
        if ( cmd.equals("markoff_proc") ) {
            warn(log, "Enabling markoff_proc profile.");
        	app.setAdditionalProfiles("markoff_proc");
        }
        else if ( cmd.equals("svc") ) {
            // Enable ServiceConfiguration
            warn(log, "Enabling svc profile.");
            app.setAdditionalProfiles("svc");
        }
        else if ( cmd.equals("shell") ) {
        	// Enable all of the ShellComponent classes
            warn(log, "Enabling shell profile.");
            app.setAdditionalProfiles("shell");
        }
        warn(log, String.format("cmd=%s.  argList=%s", cmd, argList));
        app.run(argList.toArray(new String[0]));
    }
}
