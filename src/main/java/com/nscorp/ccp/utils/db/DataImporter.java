package com.nscorp.ccp.utils.db;

import lombok.extern.slf4j.Slf4j;
import lombok.val;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.time.LocalDate;
import java.time.LocalDateTime;

import static com.nscorp.ccp.utils.logging.LogUtils.warn;

/**
 * Data importer utility for loading test data into the database.
 */
@Slf4j
public class DataImporter {
    
    /**
     * Imports test data into the database.
     * This method can be extended to load specific test data as needed.
     *
     * @param conn The database connection
     */
    public void importTestData(Connection conn) {
        try {
            warn(log, "Starting test data import...");

            // Check if tables exist before importing data
            if (tablesExist(conn)) {
                // Import test markoff studies
                importMarkoffStudies(conn);

                // Import test markoff data
                importMarkoffData(conn);

                warn(log, "Test data import completed successfully");
            } else {
                warn(log, "Tables do not exist yet, skipping test data import");
            }
        } catch (Exception e) {
            warn(log, String.format("Error importing test data: %s", e.getMessage()));
            // Don't throw exception to allow database initialization to continue
            warn(log, "Continuing without test data...");
        }
    }

    private boolean tablesExist(Connection conn) {
        try {
            val stmt = conn.createStatement();
            stmt.executeQuery("SELECT COUNT(*) FROM ccp.markoff_study WHERE 1=0");
            stmt.close();
            return true;
        } catch (SQLException e) {
            return false;
        }
    }
    
    private void importMarkoffStudies(Connection conn) throws SQLException {
        val sql = """
            INSERT INTO ccp.markoff_study 
            (run_ts, start_date, end_date, status, description, creation_user, error_message) 
            VALUES (?, ?, ?, ?, ?, ?, ?)
            """;
        
        try (PreparedStatement stmt = conn.prepareStatement(sql)) {
            // Test study 1 - Completed
            stmt.setTimestamp(1, java.sql.Timestamp.valueOf(LocalDateTime.of(2023, 6, 1, 10, 0, 0)));
            stmt.setDate(2, java.sql.Date.valueOf(LocalDate.of(2023, 1, 1)));
            stmt.setDate(3, java.sql.Date.valueOf(LocalDate.of(2023, 6, 30)));
            stmt.setString(4, "COMPLETED");
            stmt.setString(5, "Test markoff study 1");
            stmt.setString(6, "testuser");
            stmt.setString(7, null);
            stmt.addBatch();
            
            // Test study 2 - Failed
            stmt.setTimestamp(1, java.sql.Timestamp.valueOf(LocalDateTime.of(2023, 6, 2, 11, 0, 0)));
            stmt.setDate(2, java.sql.Date.valueOf(LocalDate.of(2023, 2, 1)));
            stmt.setDate(3, java.sql.Date.valueOf(LocalDate.of(2023, 7, 31)));
            stmt.setString(4, "FAILED");
            stmt.setString(5, "Test markoff study 2");
            stmt.setString(6, "testuser");
            stmt.setString(7, "Test error message");
            stmt.addBatch();
            
            // Test study 3 - In Progress
            stmt.setTimestamp(1, java.sql.Timestamp.valueOf(LocalDateTime.of(2023, 6, 3, 12, 0, 0)));
            stmt.setDate(2, java.sql.Date.valueOf(LocalDate.of(2023, 3, 1)));
            stmt.setDate(3, java.sql.Date.valueOf(LocalDate.of(2023, 8, 31)));
            stmt.setString(4, "IN_PROGRESS");
            stmt.setString(5, "Test markoff study 3");
            stmt.setString(6, "testuser");
            stmt.setString(7, null);
            stmt.addBatch();
            
            stmt.executeBatch();
            warn(log, "Imported test markoff studies");
        }
    }
    
    private void importMarkoffData(Connection conn) throws SQLException {
        val sql = """
            INSERT INTO ccp.markoff_data 
            (markoff_study_id, distr, sub_distr, pool_name, craft, distinct_cnt_emp, 
             emp_total_state_days_median, total_state_days, state_days, rate, emp_total_state_days_avg) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """;
        
        try (PreparedStatement stmt = conn.prepareStatement(sql)) {
            // Sample markoff data for study 1
            stmt.setLong(1, 1L); // markoff_study_id
            stmt.setString(2, "01"); // distr
            stmt.setString(3, "01"); // sub_distr
            stmt.setString(4, "AA"); // pool_name
            stmt.setString(5, "TC"); // craft
            stmt.setDouble(6, 10.0); // distinct_cnt_emp
            stmt.setDouble(7, 250.5); // emp_total_state_days_median
            stmt.setDouble(8, 2500.0); // total_state_days
            stmt.setDouble(9, 200.0); // state_days
            stmt.setDouble(10, 0.08); // rate
            stmt.setDouble(11, 260.0); // emp_total_state_days_avg
            stmt.addBatch();
            
            // Sample markoff data for study 1 - different district
            stmt.setLong(1, 1L);
            stmt.setString(2, "02");
            stmt.setString(3, "01");
            stmt.setString(4, "BB");
            stmt.setString(5, "EN");
            stmt.setDouble(6, 15.0);
            stmt.setDouble(7, 300.0);
            stmt.setDouble(8, 4500.0);
            stmt.setDouble(9, 350.0);
            stmt.setDouble(10, 0.078);
            stmt.setDouble(11, 310.0);
            stmt.addBatch();
            
            stmt.executeBatch();
            warn(log, "Imported test markoff data");
        }
    }
}
