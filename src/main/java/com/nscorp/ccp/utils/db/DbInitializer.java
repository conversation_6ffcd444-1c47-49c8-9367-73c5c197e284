package com.nscorp.ccp.utils.db;
import java.util.*;

import lombok.Builder;
import lombok.val;

import static com.google.common.collect.ImmutableList.*;
import static com.nscorp.ieorcommons.guava.ImmutableCollectionUtils.*;
import static com.nscorp.ieorcommons.lang.exception.NSExceptionUtils.*;
import static com.nscorp.ccp.utils.logging.LogUtils.*;

import com.google.common.base.Stopwatch;
import lombok.*;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;

import java.io.*;
import java.sql.*;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.stream.Stream;

import static com.google.common.collect.Iterables.concat;
import static com.google.common.collect.Iterables.filter;
import static org.apache.commons.io.FileUtils.*;

@RequiredArgsConstructor
public class DbInitializer {
	private static final Logger log = org.slf4j.LoggerFactory.getLogger(DbInitializer.class);

	private final DbPopulator dbPopulator;
	private final DataImporter dataImporter;

	private String editSql(String sParam) {
		return Optional.of(sParam).
				map(s->StringUtils.replace(s, "generated by default as identity primary key", "generated always as identity")).
				map(s->StringUtils.replace(s, "in scpm_rpt_usr index in scpm_rpt_idx", "")).
				map(s->StringUtils.replace(s, "in scpm_inp_usr index in scpm_inp_idx", "")).
				get();
	}
	private Iterable<File> listCreates(final String... dir) {
		return Stream.of(dir).
				flatMap(d->listFiles(new File(d), new String[]{"sql"}, false).stream()).
				filter(e->e.getName().startsWith("create_")).
				collect(toImmutableList());
	}
	private List<File> listSqlFiles() {
		val additional = imlist(new File("ddl/db2/ccp/testing/additional_ddl.sql"));
		val tablesAndViews = listCreates("ddl/db2/ccp/tables");
		val tables = filter(tablesAndViews, e->! e.getName().endsWith("_view.sql"));
		val views = listCreates();
		// val indexes = listCreates("ddl/db2/ccp/indexes");
		val indexes = listCreates();
		return imlist(concat(additional, tables, views, indexes));
	}


	@Value
	@Builder
	private static class ConnectInfo {
		String jdbcUrl;
		String username;
		String password;
		String driverClassName;
	}

	private static final String DRIVER = "org.hsqldb.jdbc.JDBCDriver";
	private static final String URL = "****************************************";
	private static final String USER = "sa";
	private static final String PASSWORD = "";
	@SneakyThrows private Connection connect(ConnectInfo ci) {
		val conn = DriverManager.getConnection(ci.jdbcUrl, ci.username, ci.password);
		conn.setAutoCommit(true);
		return conn;
	}
	private static boolean initialized = false;

	public void initdb() {
		initdb(URL, USER, PASSWORD, DRIVER);
	}
	@SneakyThrows public void initdb(
			final String jdbcUrl,
			final String username,
			final String password,
			final String driverClassName ) {
		if ( initialized ) {
			return;
		}
		log.warn("Starting DbInitializer.initdb()");
		initialized = true;
		val ci = ConnectInfo.builder().jdbcUrl(jdbcUrl).username(username).password(password).driverClassName(driverClassName).build();
		val stopwatch = Stopwatch.createStarted();
		warn(log, "Starting initdb()");
		Class.forName(driverClassName);
		@Cleanup val conn = connect(ci);

		// Load the DDL.
		applyDdl(conn);

		val executor = Executors.newWorkStealingPool(8);

		executor.shutdown();
		executor.awaitTermination(Long.MAX_VALUE, TimeUnit.MILLISECONDS);
		warn(log, String.format("Entire DB initialization took %d milliseconds.", stopwatch.elapsed().toMillis()));
	}

	private void applyDdl(Connection conn) {
		val stopwatch = Stopwatch.createStarted();
		listSqlFiles().stream().forEachOrdered(file->{
			warn(log, String.format("Executing this file: %s", file));
			final String s = editSql(sneakyThrow(()-> readFileToString(file, "UTF-8")));
			dbPopulator.populateDb(conn, s);
		});
		warn(log, String.format("applying DDL took %d milliseconds.", stopwatch.elapsed().toMillis()));
	}

}
