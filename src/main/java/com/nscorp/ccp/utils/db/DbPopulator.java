package com.nscorp.ccp.utils.db;

import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.lang3.StringUtils;

import java.sql.Connection;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.Arrays;

import static com.nscorp.ccp.utils.logging.LogUtils.warn;

/**
 * Database populator utility for executing DDL statements.
 */
@Slf4j
public class DbPopulator {
    
    /**
     * Populates the database by executing the provided SQL script.
     * 
     * @param conn The database connection
     * @param sql The SQL script to execute
     */
    public void populateDb(Connection conn, String sql) {
        if (StringUtils.isBlank(sql)) {
            warn(log, "Empty SQL script provided, skipping...");
            return;
        }
        
        try {
            // Split SQL by semicolons and execute each statement
            val statements = Arrays.stream(sql.split(";"))
                    .map(String::trim)
                    .filter(s -> !s.isEmpty())
                    .filter(s -> !s.startsWith("--")) // Skip comments
                    .toArray(String[]::new);
            
            try (Statement stmt = conn.createStatement()) {
                for (String sqlStatement : statements) {
                    if (StringUtils.isNotBlank(sqlStatement)) {
                        try {
                            warn(log, String.format("Executing SQL: %s", 
                                sqlStatement.length() > 100 ? 
                                    sqlStatement.substring(0, 100) + "..." : 
                                    sqlStatement));
                            stmt.execute(sqlStatement);
                        } catch (SQLException e) {
                            // Log the error but continue with other statements
                            warn(log, String.format("Failed to execute SQL statement: %s. Error: %s", 
                                sqlStatement, e.getMessage()));
                            // Don't throw exception to allow other statements to execute
                        }
                    }
                }
            }
        } catch (Exception e) {
            warn(log, String.format("Error executing SQL script: %s", e.getMessage()));
            throw new RuntimeException("Failed to populate database", e);
        }
    }
}
