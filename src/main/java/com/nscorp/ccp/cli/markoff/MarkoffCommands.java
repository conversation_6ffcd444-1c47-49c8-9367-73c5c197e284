package com.nscorp.ccp.cli.markoff;


import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.FluentIterable;
import com.nscorp.ccp.cli.CommandGroups;
import com.nscorp.ccp.common.markoff.MarkoffInputData;
import com.nscorp.ccp.common.markoff.MarkoffStudy;
import com.nscorp.ccp.common.markoff.MarkoffStudyStatus;
import com.nscorp.ccp.dao.markoff.MarkoffInputDataDAO;
import com.nscorp.ccp.dao.markoff.MarkoffAndStudyDataDAO;
import com.nscorp.ccp.logic.markoff.*;
import com.nscorp.ccp.utils.json.JsonUtils;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Profile;
import org.springframework.shell.standard.ShellComponent;
import org.springframework.shell.standard.ShellMethod;

import java.io.File;
import java.io.IOException;
import java.time.Instant;
import java.time.LocalDate;
import java.util.Collection;

import static com.nscorp.ccp.utils.json.JsonUtils.OBJECT_MAPPER;

@ShellComponent
@Slf4j
@RequiredArgsConstructor
@Profile("shell")
public class MarkoffCommands {
	private final MarkoffInputDataDAO markoffInputDataDAO;
	private final MarkoffAndStudyDataDAO markoffAndStudyDataDAO;
	private final MarkoffProcessorLogic markoffProcessorLogic = new MarkoffProcessorLogic();
	@ShellMethod(group = CommandGroups.MARKOFF, key = "download-markoff-input-data", value="Download markoff input data")
	public Iterable<MarkoffInputData> downloadMarkoffInputData(LocalDate startDt, Integer studyLengthMonths) {
		val data = markoffInputDataDAO.getMarkoffInputData(startDt, studyLengthMonths);
		return data;
	}

	@SneakyThrows @ShellMethod(group = CommandGroups.MARKOFF, key = "reformat-intermediates", value="Load intermediates from a file, reformat, and write a new file.")
	public void reformatIntermediates(String inFilename, String outFilename) {
		val intermediates = JsonUtils.OBJECT_MAPPER.readValue(new File(inFilename), new TypeReference<Collection<Intermediate>>(){});
		val reformatted = new IntermediateReformatter().reformat(intermediates);
		JsonUtils.OBJECT_MAPPER.writerWithDefaultPrettyPrinter().writeValue(new File(outFilename), reformatted);
	}

	@ShellMethod(group = CommandGroups.MARKOFF, key = "read-intermediate-csv", value="Read intermediate CSV")
	public void readIntermediateCsv(String filename) throws IOException {
		val intermediates = ReadCsv.readCsv(filename);
		val newI = FluentIterable.from(intermediates).transform(i ->
								i.withRc(i.getRc() != null ? i.getRc().trim() : "").
								withTrnCrewDistr(i.getTrnCrewDistr() != null ? i.getTrnCrewDistr().trim() : "").
								withYardAsgnSeq(i.getYardAsgnSeq() != null ? i.getYardAsgnSeq().trim() : "").
								withYardAsgnType(i.getYardAsgnType() != null ? i.getYardAsgnType().trim() : "").
								withStartSubFuncDesc(i.getStartSubFuncDesc() != null ? i.getStartSubFuncDesc().trim() : "").
								withEndSubFuncDesc(i.getEndSubFuncDesc() != null ? i.getEndSubFuncDesc().trim() : "").
								withLoType(i.getLoType() != null ? i.getLoType().trim() : "").
								withFinalOs(i.getFinalOs() != null ? i.getFinalOs().trim(): "").
								withEahXb(i.getEahXb() != null  ? i.getEahXb().trim() : "").
								withEahPool(i.getEahPool() != null ? i.getEahPool().trim() : "").
								withHomeAwayInd(i.getHomeAwayInd() != null ? i.getHomeAwayInd().trim() : "").
								withFinalPool(i.getFinalPool() != null ? i.getFinalPool().trim() : "").
								withAwayPool(i.getAwayPool() != null ? i.getAwayPool().trim() : "")



		);
		OBJECT_MAPPER.writerWithDefaultPrettyPrinter()
				.writeValue(new File(filename + ".json"), newI);//intermediates);

	}

	@ShellMethod(group = CommandGroups.MARKOFF, key = "read-csv-file-and-process-markoff-data", value="Read CSV files for Markoff Input and Markoff pool and use this to process Markoff data given filename and study length months")
	public void readCsvFileAndProcessMarkoffData(String markoffInputFilename, String markoffPoolFilename, Integer studyLengthMonths){
		val inputData = FluentIterable.from(ReadCsv.readMarkoffInputDataCsv(markoffInputFilename));
		val poolData = ReadCsv.readMarkoffPoolInputDataCsv(markoffPoolFilename);
		//loadAndCompare(step1ToTest, "after_step1.csv");
		final LocalDate todayDate = LocalDate.now();
		final LocalDate endDateFuture = todayDate.withDayOfMonth(1);
		//final LocalDate startDatePast = endDateFuture.minusMonths(studyLengthMonths + 1);
		val resultData = markoffProcessorLogic.process(inputData, poolData, endDateFuture, studyLengthMonths);

		//val endDate = inputData.filter()
	}

	@ShellMethod(group = CommandGroups.MARKOFF, key = "download-and-process-markoff-input-data", value="Download and Process markoff input data")
	public void downloadAndProcessMarkoffInputDataFromToday(Integer studyLengthMonths) {


		final LocalDate todayDate = LocalDate.now();
		final LocalDate endDateFuture = todayDate.withDayOfMonth(1);
		final LocalDate startDatePast = endDateFuture.minusMonths(studyLengthMonths + 1);
		final LocalDate startDateToSaveToDB = endDateFuture.minusMonths(studyLengthMonths);
		final LocalDate endDateToSaveToDb = endDateFuture.minusDays(1);
		System.out.println("START Date Past: " + startDatePast);
		System.out.println("END Date Future:" + endDateFuture);
		System.out.println("STUDY LENGTH MONTHS: " + studyLengthMonths);
		log.warn(String.format("Starting program. Memory = %d", Runtime.getRuntime().totalMemory()));

		val inputData = markoffInputDataDAO.getMarkoffInputDataByDates(startDatePast, endDateFuture, studyLengthMonths);

		log.warn(String.format("Get Markoff Input Data. Memory = %d", Runtime.getRuntime().totalMemory()));

		val poolData = markoffInputDataDAO.getMarkoffPoolData();

		log.warn(String.format("Get Markoff Pool Data. Memory = %d", Runtime.getRuntime().totalMemory()));

		val resultData = markoffProcessorLogic.process(inputData, poolData, endDateFuture, studyLengthMonths);

/*		val markoffStudy = MarkoffStudy.builder()
				.runTs(Instant.now())
				.startDate(startDateToSaveToDB)
				.endDate(endDateToSaveToDb)
				.status("Creating")
				.description("")
				.creationUser("")
				.errorMessage("").build();
		val updatedMarkoffStudy =  markoffAndStudyDataDAO.save(markoffStudy);

		System.out.println("Markoff Study: " + updatedMarkoffStudy);
		val markoffDataWithStudyID = FluentIterable.from(resultData).transform(rD -> rD.withMarkoffStudyId(updatedMarkoffStudy.getMarkoffStudyId()));

		val savedMarkoffData = markoffAndStudyDataDAO.saveAll(markoffDataWithStudyID);
		val markoffStudyUpdateSuccessful = MarkoffStudy.builder()
				.markoffStudyId(updatedMarkoffStudy.getMarkoffStudyId())
				.runTs(Instant.now())
				.startDate(startDateToSaveToDB)
				.endDate(endDateToSaveToDb)
				.status("Successful")
				.description("Successful update of MarkoffData")
				.creationUser("")
				.errorMessage("").build();
		val successfulMarkoffStudy = markoffAndStudyDataDAO.save(markoffStudyUpdateSuccessful);
		System.out.println(successfulMarkoffStudy);
		System.out.println(FluentIterable.from(savedMarkoffData).first());*/
	}

	@ShellMethod(group = CommandGroups.MARKOFF, key = "download-and-process-markoff-input-data-from-specific-dates", value="Download and Process markoff input data from specific date")
	public void downloadAndProcessMarkoffInputDataFromSpecificDates(String startDateStr, String endDateStr, Integer studyLengthMonths) {

		final LocalDate startDate = LocalDate.parse(startDateStr);
		final LocalDate endDate = LocalDate.parse(endDateStr);
		System.out.println("START DATE: " + startDate);
		System.out.println("END DATE: " + endDate);
		System.out.println("STUDY LEN MONTHS: " + studyLengthMonths);
		log.warn(String.format("Starting program. Memory = %d", Runtime.getRuntime().totalMemory()));
		val inputData = markoffInputDataDAO.getMarkoffInputDataByDates(startDate, endDate, studyLengthMonths);
		log.warn(String.format("Get Markoff Input Data. Memory = %d", Runtime.getRuntime().totalMemory()));

		val poolData = markoffInputDataDAO.getMarkoffPoolData();
		log.warn(String.format("Get Markoff Pool Data. Memory = %d", Runtime.getRuntime().totalMemory()));

		val resultData = markoffProcessorLogic.process(inputData, poolData, endDate, studyLengthMonths);

		val markoffStudy = MarkoffStudy.builder()
				.runTs(Instant.now())
				.startDate(startDate)
				.endDate(endDate)
				.status(MarkoffStudyStatus.RUNNING)
				.description("")
				.creationUser("")
				.errorMessage("").build();
		val updatedMarkoffStudy =  markoffAndStudyDataDAO.save(markoffStudy);
		System.out.println("Markoff Study: " + updatedMarkoffStudy);
		val markoffDataWithStudyID = FluentIterable.from(resultData).transform(rD -> rD.withMarkoffStudyId(updatedMarkoffStudy.getMarkoffStudyId()));
		val savedMarkoffData = markoffAndStudyDataDAO.saveAll(markoffDataWithStudyID);
		val markoffStudyUpdateSuccessful = MarkoffStudy.builder()
				.markoffStudyId(updatedMarkoffStudy.getMarkoffStudyId())
				.runTs(Instant.now())
				.startDate(startDate)
				.endDate(endDate)
				.status(MarkoffStudyStatus.COMPLETED)
				.description("Successful update of MarkoffData")
				.creationUser("")
				.errorMessage("").build();
		val successfulMarkoffStudy = markoffAndStudyDataDAO.save(markoffStudyUpdateSuccessful);
		System.out.println(successfulMarkoffStudy);
		System.out.println(FluentIterable.from(savedMarkoffData).first());
	}
}
