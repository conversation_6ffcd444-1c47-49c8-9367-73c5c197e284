package com.nscorp.ccp.testing.util;

import com.nscorp.ccp.TestConfig;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.boot.autoconfigure.ImportAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@ExtendWith(SpringExtension.class)
@SpringBootTest(properties = {"spring.shell.interactive.enabled=false"}, args = {"test"}, webEnvironment = SpringBootTest.WebEnvironment.DEFINED_PORT)
@ActiveProfiles(profiles = {"test", "hsqldb-test", "timing"})
@ImportAutoConfiguration({TestConfig.class})
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@Slf4j
public abstract class SpringBasedTest {
}
