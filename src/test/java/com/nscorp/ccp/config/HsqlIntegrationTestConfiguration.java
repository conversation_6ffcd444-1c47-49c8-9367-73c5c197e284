package com.nscorp.ccp.config;

import com.nscorp.ccp.biz.impl.markoff.MarkoffProcessorImpl;
import com.nscorp.ccp.biz.markoff.MarkoffProcessor;
import com.nscorp.ccp.biz.seedData.SeedDataLoader;
import com.nscorp.ccp.dao.impl.markoff.MarkoffInputDataDAOImpl;
import com.nscorp.ccp.dao.impl.markoff.MarkoffAndStudyDAOImpl;
import com.nscorp.ccp.dao.markoff.MarkoffInputDataDAO;
import com.nscorp.ccp.dao.markoff.MarkoffAndStudyDataDAO;
import com.nscorp.ccp.utils.date.DateTimeProvider;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;

import javax.sql.DataSource;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Date;

/**
 * Integration test configuration that uses real DAO implementations with HSQL database
 * instead of mocked DAOs. This allows for true integration testing of the database layer.
 */
@TestConfiguration
@Slf4j
public class HsqlIntegrationTestConfiguration {

    @Bean
    @Primary
    public DateTimeProvider testDateTimeProvider() {
        return new DateTimeProvider() {
            @Override
            public Instant now() {
                return Instant.parse("2023-06-01T10:00:00Z");
            }

            @Override
            public LocalDate today() {
                return LocalDate.of(2023, 6, 1);
            }

            @Override
            public Date getDate() {
                return Date.from(LocalDate.of(2023, 6, 1).atStartOfDay(ZoneId.systemDefault()).toInstant());
            }

            @Override
            public void setTime(Instant instant) {
                // No-op for test implementation
            }
        };
    }

    @Bean
    @Primary
    public MarkoffInputDataDAO markoffInputDataDAO(@Qualifier("dataWarehouseDataSource") DataSource dataSource) {
        log.info("Creating real MarkoffInputDataDAOImpl for integration tests");
        return new MarkoffInputDataDAOImpl(dataSource);
    }

    // Note: MarkoffAndStudyDataDAO uses Spring Data repositories, so it will be auto-configured
    // We don't need to manually create it here as it uses @Repository annotation

    // Note: MarkoffProcessorImpl will be auto-configured as it's a @Service
    // We don't need to manually create it here

    @Bean
    @Primary
    public SeedDataLoader testSeedDataLoader(DateTimeProvider dateTimeProvider) {
        return new SeedDataLoader() {
            @Override
            public void load(Instant cutoffTs) {
                log.info("Test SeedDataLoader.load() called with cutoffTs: {}", cutoffTs);
                // No-op for test implementation
            }

            @Override
            public void load(boolean force, Instant cutoffTs) {
                log.info("Test SeedDataLoader.load() called with force: {}, cutoffTs: {}", force, cutoffTs);
                // No-op for test implementation
            }
        };
    }
}
