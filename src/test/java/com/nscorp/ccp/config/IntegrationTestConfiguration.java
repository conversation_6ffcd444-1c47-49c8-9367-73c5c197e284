package com.nscorp.ccp.config;

import com.nscorp.ccp.biz.impl.markoff.MarkoffProcessorImpl;
import com.nscorp.ccp.biz.markoff.MarkoffProcessor;
import com.nscorp.ccp.biz.seedData.SeedDataLoader;
import com.nscorp.ccp.dao.markoff.MarkoffInputDataDAO;
import com.nscorp.ccp.dao.markoff.MarkoffAndStudyDataDAO;
import com.nscorp.ccp.common.markoff.MarkoffStudy;
import com.nscorp.ccp.utils.date.DateTimeProvider;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Collections;
import java.util.Date;

@TestConfiguration
public class IntegrationTestConfiguration {

    // Removed database configuration since we don't need it for these integration tests

    @Bean
    @Primary
    public DateTimeProvider testDateTimeProvider() {
        return new DateTimeProvider() {
            @Override
            public Instant now() {
                return Instant.parse("2023-06-01T10:00:00Z");
            }

            @Override
            public LocalDate today() {
                return LocalDate.of(2023, 6, 1);
            }

            @Override
            public Date getDate() {
                return Date.from(LocalDate.of(2023, 6, 1).atStartOfDay(ZoneId.systemDefault()).toInstant());
            }

            @Override
            public void setTime(Instant instant) {
                // No-op for test implementation
            }
        };
    }

    @Bean
    @Primary
    public MarkoffInputDataDAO testMarkoffInputDataDAO() {
        return new MarkoffInputDataDAO() {
            @Override
            public Iterable getMarkoffInputData(LocalDate startDate, Integer studyLengthMonths) {
                // Return empty data for integration tests to avoid complex setup
                return Collections.emptyList();
            }

            @Override
            public Iterable getMarkoffInputDataByDates(LocalDate startDatePast, LocalDate endDateFuture, Integer studyLengthMonths) {
                // Return empty data for integration tests to avoid complex setup
                return Collections.emptyList();
            }

            @Override
            public Iterable getMarkoffPoolData() {
                // Return empty data for integration tests to avoid complex setup
                return Collections.emptyList();
            }
        };
    }

    @Bean
    @Primary
    public MarkoffAndStudyDataDAO testMarkoffAndStudyDataDAO() {
        return new MarkoffAndStudyDataDAO() {
            @Override
            public MarkoffStudy getLatestSuccessfulStudy() {
                return null; // Return null for integration tests
            }

            @Override
            public MarkoffStudy getStudyById(long studyId) {
                return null; // Return null for integration tests
            }

            @Override
            public Iterable getAllStudies() {
                return Collections.emptyList(); // Return empty list for integration tests
            }

            @Override
            public Iterable getMarkoffDataByStudyId(long studyId) {
                return Collections.emptyList(); // Return empty list for integration tests
            }

            @Override
            public Iterable saveAll(Iterable entities) {
                // For integration tests, just return the input entities
                return entities;
            }

            @Override
            public MarkoffStudy save(MarkoffStudy markoffStudy) {
                // For integration tests, just return the input entity
                return markoffStudy;
            }
        };
    }

    @Bean
    @Primary
    public MarkoffProcessor testMarkoffProcessor(
            MarkoffInputDataDAO markoffInputDataDAO,
            MarkoffAndStudyDataDAO markoffAndStudyDataDAO) {
        return new MarkoffProcessorImpl(markoffInputDataDAO, markoffAndStudyDataDAO);
    }

    @Bean
    @Primary
    public SeedDataLoader testSeedDataLoader(DateTimeProvider dateTimeProvider) {
        return new SeedDataLoader() {
            @Override
            public void load(Instant cutoffTs) {
                // No-op for test implementation
            }

            @Override
            public void load(boolean force, Instant cutoffTs) {
                // No-op for test implementation
            }
        };
    }
}
