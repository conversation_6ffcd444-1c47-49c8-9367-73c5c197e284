package com.nscorp.ccp;

import com.nscorp.ccp.testing.util.LogOutputInitializer;
import com.nscorp.ccp.utils.date.DateTimeProvider;
import com.nscorp.ccp.utils.db.*;
import com.zaxxer.hikari.HikariDataSource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;

import javax.sql.DataSource;

@TestConfiguration
@Slf4j
@RequiredArgsConstructor
public class TestConfig {
	@Bean("scpmDataSource")
	@Primary
	public DataSource scpmDataSource(){
		return buildDs(true);
	}

	private String jdbcUrl = "****************************************";
	private String username = "sa";
	private String password = "";
	private String driverClassName = "org.hsqldb.jdbc.JDBCDriver";

	@Value("${skipTestInitialization}")
	private boolean skipTestInitialization;

	private boolean schemaCreated = false;
	private boolean dbInitialized = false;

	private synchronized DataSource buildDs(boolean setSchema) {
		if ( ! skipTestInitialization ) {
			LogOutputInitializer.init();
			if ( ! dbInitialized ) {
				new DbInitializer(new DbPopulator(), new DataImporter()).initdb(jdbcUrl, username, password, driverClassName);
				dbInitialized = true;
			}
		}
		final HikariDataSource result = new HikariDataSource();
		result.setJdbcUrl(jdbcUrl);
		result.setUsername(username);
		result.setPassword(password);
		result.setDriverClassName(driverClassName);
		if ( setSchema ) {
			result.setSchema("CCP");
			result.setConnectionInitSql("set schema CCP");
		}
		return result;
	}
}
