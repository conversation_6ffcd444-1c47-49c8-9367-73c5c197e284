# HSQL Database Testing Setup

This document describes the complete HSQL database testing setup for the or-ccp-markoff project, enabling integration tests with an in-memory database instead of connecting to the real database.

## Overview

The HSQL testing setup provides:
- In-memory HSQL database for integration tests
- Automatic database schema creation from DDL files
- Test data loading for realistic testing scenarios
- Real DAO implementations instead of mocked ones
- Complete integration testing capabilities

## Components

### 1. Dependencies (build.gradle)

The following test dependencies have been added:
```gradle
testImplementation 'org.springframework.boot:spring-boot-starter-jdbc:2.7.5'
testImplementation 'com.zaxxer:HikariCP'
testImplementation 'org.hsqldb:hsqldb:2.5.1'
```

### 2. Configuration Files

#### src/test/resources/application.properties
```properties
# HSQL Database Configuration
spring.datasource.driver-class-name=org.hsqldb.jdbc.JDBCDriver
spring.datasource.url=****************************************
spring.datasource.username=sa
spring.datasource.password=

# Spring Configuration
spring.main.allow-bean-definition-overriding=true
spring.main.allow-circular-references=true
skipTestInitialization=false

# Test-specific settings
testTransactions=true
```

### 3. Database Initialization Classes

#### DbPopulator.java
- Executes DDL statements to create database schema
- Handles SQL script parsing and execution
- Provides error handling and logging

#### DataImporter.java
- Loads test data into the database
- Creates sample markoff studies and data
- Supports extensible test data scenarios

#### DbInitializer.java (Enhanced)
- Orchestrates database setup process
- Loads DDL files from `ddl/db2/ccp/` directory
- Calls DbPopulator and DataImporter in sequence

### 4. Test Configuration Classes

#### TestConfig.java (Enhanced)
- Creates HSQL DataSource beans
- Configures JdbcTemplate and NamedParameterJdbcTemplate
- Manages database initialization lifecycle

#### HsqlIntegrationTestConfiguration.java
- Provides real DAO implementations for integration tests
- Configures test-specific DateTimeProvider
- Replaces mocked DAOs with database-backed implementations

## Usage

### Writing Integration Tests

Use the `HsqlIntegrationTestConfiguration` for tests that need real database operations:

```java
@Tag("slow")
@SpringBootTest
@Import({TestConfig.class, HsqlIntegrationTestConfiguration.class})
@ActiveProfiles("test")
class MyIntegrationTest extends SpringBasedTest {
    
    @Autowired
    private MarkoffInputDataDAO markoffInputDataDAO;
    
    @Test
    void testDatabaseOperation() {
        // Test with real database operations
        val data = markoffInputDataDAO.getMarkoffInputData(LocalDate.now(), 6);
        assertNotNull(data);
    }
}
```

### Golden Master Testing

The setup supports golden master testing patterns:

```java
@Test
void test_Integration() {
    TestUtils.runTestAndAssert("TestName", () -> {
        // Load test data
        val data = markoffInputDataDAO.getMarkoffInputData(startDate, months);
        
        // Process data
        val result = processor.process(data);
        
        // Return formatted result for comparison
        return JsonUtils.prettyPrint(result);
    });
}
```

## Database Schema

The HSQL database automatically creates the following tables:
- `ccp.markoff_study` - Stores markoff study metadata
- `ccp.markoff_data` - Stores markoff calculation results

Test data includes:
- 3 sample markoff studies (COMPLETED, FAILED, IN_PROGRESS)
- Sample markoff data records for testing

## Running Tests

### All Tests
```bash
./gradlew test
```

### Integration Tests Only
```bash
./gradlew test --tests "*Integration*"
```

### With Coverage
```bash
./gradlew test jacocoTestReport
```

## Benefits

1. **Real Database Operations**: Tests use actual DAO implementations instead of mocks
2. **Fast Execution**: In-memory database provides quick test execution
3. **Isolation**: Each test run gets a fresh database instance
4. **Realistic Testing**: Tests run against actual SQL queries and database constraints
5. **Coverage**: Achieves better code coverage by testing DAO implementations

## Troubleshooting

### Common Issues

1. **Database Not Initialized**: Ensure `skipTestInitialization=false` in test properties
2. **DDL Errors**: Check DDL files in `ddl/db2/ccp/` for HSQL compatibility
3. **Connection Issues**: Verify HSQL dependency is included in test classpath

### Debugging

Enable SQL logging by adding to test properties:
```properties
logging.level.org.springframework.jdbc.core=DEBUG
logging.level.org.hsqldb=DEBUG
```

## Extending the Setup

### Adding Test Data

Modify `DataImporter.java` to add more test scenarios:
```java
private void importCustomTestData(Connection conn) throws SQLException {
    // Add your test data here
}
```

### Custom Test Configuration

Create additional test configuration classes for specific test scenarios:
```java
@TestConfiguration
public class CustomTestConfiguration {
    // Custom beans for specific tests
}
```

This setup provides a robust foundation for integration testing with HSQL database, enabling comprehensive testing of DAO implementations and database interactions.
