buildscript {
	repositories {
		maven {
			url "http://repository.nscorp.com:8084/nexus/content/groups/NSRepository"
			allowInsecureProtocol = true
		}
	}
	dependencies {
		classpath 'org.springframework.boot:spring-boot-gradle-plugin:2.7.5'
		classpath "org.sonatype.gradle.plugins:scan-gradle-plugin:2.0.4"
	}
}

apply plugin: 'java'
apply plugin: 'org.springframework.boot'
apply plugin: 'io.spring.dependency-management'
apply plugin: 'jacoco'
apply plugin: "org.sonatype.gradle.plugins.scan"

group = 'com.nscorp'
sourceCompatibility = "17"
targetCompatibility = "17"

repositories {
	maven {
		url "http://repository.nscorp.com:8084/nexus/content/groups/NSRepository"
		allowInsecureProtocol = true
	}
	maven {
		url "http://nsos-pr1-nex.atldc.nscorp.com:8081/repository/ns-shared-jars"
		allowInsecureProtocol = true
	}
	maven {
		url "http://nsos-pr1-nex.atldc.nscorp.com:8081/repository/develop-mvn"
		allowInsecureProtocol = true
	}
}

dependencies {
	implementation("org.springframework.boot:spring-boot-starter:2.7.5")
	implementation("org.springframework.boot:spring-boot-starter-logging:2.7.5")
	implementation ("org.springframework.boot:spring-boot-starter-validation:2.7.5")
	implementation( 'org.springframework.boot:spring-boot-starter-data-jdbc:3.3.3' )
	implementation( 'org.springframework.shell:spring-shell-starter:2.0.0.RELEASE' )
	implementation('one.util:streamex:0.8.2')

	implementation 'org.springframework.boot:spring-boot-starter-web:2.7.5'
	implementation 'org.springframework.boot:spring-boot-starter-actuator:2.7.5'
	implementation( 'org.assertj:assertj-core:3.22.0' )
	implementation( 'javax.xml.bind:jaxb-api:2.3.1' )
	implementation( 'org.mockito:mockito-core:4.4.0' )
	implementation( 'org.mockito:mockito-inline:4.4.0' )
	implementation( 'org.jetbrains:annotations:16.0.2' )
	testImplementation 'org.springframework.boot:spring-boot-starter-test:2.7.5'
	implementation files("lib/or-ieorcommons-v0.05.jar")
	implementation files("lib/or-ccp-common-v0.01.jar")
	implementation group: 'org.owasp.esapi', name: 'esapi', version: '2.5.1.0'

	implementation group: 'com.veracode.annotation', name: 'VeracodeAnnotations', version: '1.2.1'
	implementation group: 'com.opencsv', name: 'opencsv', version: '5.6'

	implementation group: 'org.apache.httpcomponents', name: 'httpclient', version: '4.5.13'
	implementation group: "javax.resource", name: "connector-api", version: "1.5"
	implementation group: "com.google.guava", name: "guava", version: "$guavaVersion"
	implementation group: "commons-codec", name: "commons-codec", version: "$commonsCodecVersion"
	implementation group: "commons-dbutils", name: "commons-dbutils", version: "$commonsDbutilsVersion"
	implementation group: "commons-io", name: "commons-io", version: "$commonsIoVersion"
	implementation group: "org.apache.commons", name: "commons-collections4", version: "$commonsCollections4Version"
	implementation group: "org.apache.commons", name: "commons-lang3", version: "$commonsLang3Version"
	implementation group: "org.springframework", name: "spring-context", version: "$springVersion"
	implementation group: "org.springframework", name: "spring-core", version: "$springVersion"
	implementation group: "org.springframework", name: "spring-jdbc", version: "$springVersion"
	implementation group: "org.springframework", name: "spring-tx", version: "$springVersion"
	implementation group: "org.springframework", name: "spring-aspects", version: "$springVersion"
	implementation group: "io.vavr", name: "vavr", version: "$vavrVersion"
	implementation group: "joda-time", name: "joda-time", version: "$jodaTimeVersion"
	implementation("com.fasterxml.jackson.dataformat:jackson-dataformat-csv:2.11.3")
	implementation group: "com.fasterxml.jackson.core", name: "jackson-core", version: "$jacksonVersion"
	implementation group: "com.fasterxml.jackson.core", name: "jackson-databind", version: "$jacksonVersion"
	implementation group: "com.fasterxml.jackson.core", name: "jackson-annotations", version: "$jacksonVersion"
	implementation group: "com.fasterxml.jackson.module", name: "jackson-modules-java8", version: "$jacksonVersion"
	implementation group: "com.fasterxml.jackson.datatype", name: "jackson-datatype-guava", version: "$jacksonVersion"
	implementation group: "com.fasterxml.jackson.datatype", name: "jackson-datatype-jdk8", version: "$jacksonVersion"

	implementation group: "org.apache.poi", name: "poi", version: "$poiVersion"
	implementation group: "org.apache.poi", name: "poi-ooxml", version: "$poiVersion"
	implementation group: "org.apache.poi", name: "poi-ooxml-schemas", version: "$poiVersion"


	implementation group: "org.apache.poi", name: "poi", version: "$poiVersion"
	implementation group: "org.apache.poi", name: "poi-ooxml", version: "$poiVersion"
	implementation group: "org.apache.poi", name: "poi-ooxml-schemas", version: "$poiVersion"
	implementation group: "commons-net", name: "commons-net", version: "$commonsNetVersion"
	implementation group: "commons-lang", name: "commons-lang", version: "$commonsLangVersion"
	implementation group: "org.apache.commons", name: "commons-math3", version: "$commonsMath3Version"
	implementation group: "org.apache.commons", name: "commons-math", version: "$commonsMath2Version"



	implementation 'com.ibm.db2.jcc:db2jcc_license_cisuz:1.3.1'
	implementation 'com.ibm.db2.jcc:db2jcc:1.6.0'
	implementation 'org.hsqldb:hsqldb:2.5.1'

	implementation group: 'com.teradata.jdbc', name: 'terajdbc', version: '20.00.00.11'

	compileOnly 'org.projectlombok:lombok:1.18.24'
	annotationProcessor 'org.projectlombok:lombok:1.18.24'

	testCompileOnly 'org.projectlombok:lombok:1.18.24'
	testAnnotationProcessor 'org.projectlombok:lombok:1.18.24'

	implementation 'org.mapstruct:mapstruct:1.4.1.Final'

	annotationProcessor 'org.mapstruct:mapstruct-processor:1.4.1.Final'
}

compileJava {
	options.compilerArgs << '-parameters'
}

defaultTasks 'bootJar'

task moveJar {
	doLast {
		copy {
			from "build/libs"
			into "$rootProject.projectDir/docker"
			fileMode 511
			include "or-ccp-markoff.jar"
		}
		copy {
			from "scripts"
			into "$rootProject.projectDir/docker"
			include "run.sh"
		}
		copy {
			from "scripts"
			into "$rootProject.projectDir/docker"
			include "post_markoff.sh"
		}
		copy {
			from "scripts"
			into "$rootProject.projectDir/docker"
			include "markoff_study_job.yaml"
		}
		copy {
			from configurations.runtimeClasspath
			into "$buildDir/depjars"
		}
		copy {
			from configurations.compileClasspath
			into "$buildDir/depjars"
		}
		copy {
			from configurations.testRuntimeClasspath
			into "$buildDir/depjars"
		}
		copy {
			from configurations.testCompileClasspath
			into "$buildDir/depjars"
		}
	}
}

task cleanDockerDir {
	doLast {
		delete fileTree('docker') {
			include 'or-ccp-markoff.jar'
			include 'run.sh'
		}
	}
}

bootJar.finalizedBy moveJar
clean.finalizedBy cleanDockerDir

jacoco {
	toolVersion = "0.8.7"
	reportsDirectory = layout.buildDirectory.dir('reports/jacoco').get().asFile
}

def generatedSources = [
		'com/nscorp/ccp/Launcher.class',
		'com/nscorp/ccp/cli/**/*.class',
		'com/nscorp/ccp/rest/mappers/JsonMapperImpl.class'
]

test {
	String iTags = System.getenv("INCLUDE_TAGS");
	if ( iTags == null ) {
		iTags = "fast|slow";
	}
	System.out.printf("Using tags %s.\n", iTags);
	useJUnitPlatform {
		includeTags iTags
	}
	testLogging {
		events "PASSED", "STARTED", "FAILED", "SKIPPED"
	}
	jacoco {
		excludes = generatedSources
	}
	finalizedBy jacocoTestReport
}

jacocoTestReport {
	dependsOn test
	reports {
		xml.required = true
		csv.required = false
		html {
			required = true
			outputLocation = layout.buildDirectory.dir('reports/jacoco/html')
		}
	}

	afterEvaluate {
		classDirectories.setFrom(files(classDirectories.files.collect {
			fileTree(dir: it, exclude: generatedSources)
		}))
	}
	// Force reports to be generated even if tests fail
	onlyIf = { true }
}

// Ensure jacocoTestReport runs after test
tasks.named('check') {
	dependsOn jacocoTestReport
}
